/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountBar: typeof import('./src/components/accountBar/index.vue')['default']
    AccountItem: typeof import('./src/components/accountBar/components/AccountItem.vue')['default']
    AccountList: typeof import('./src/components/accountBar/components/AccountList.vue')['default']
    CheckLine: typeof import('./src/components/checkLine/index.vue')['default']
    CleanIcon: typeof import('./src/components/icons/CleanIcon.vue')['default']
    CommonDrawer: typeof import('./src/components/CommonDrawer/index.vue')['default']
    ContactBar: typeof import('./src/components/contactBar/index.vue')['default']
    ContactGroup: typeof import('./src/components/contactBar/components/ContactGroup.vue')['default']
    ContactInfoBar: typeof import('./src/components/contactBar/components/ContactInfoBar.vue')['default']
    ContactItem: typeof import('./src/components/contactBar/components/ContactItem.vue')['default']
    ContactList: typeof import('./src/components/contactBar/components/ContactList.vue')['default']
    ContactSetting: typeof import('./src/components/contactBar/components/ContactSetting.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    FansStatistics: typeof import('./src/components/rightMenu/components/fansStatistics.vue')['default']
    FoldLeftIcon: typeof import('./src/components/icons/FoldLeftIcon.vue')['default']
    FoldRightIcon: typeof import('./src/components/icons/FoldRightIcon.vue')['default']
    GoogleIcon: typeof import('./src/components/icons/GoogleIcon.vue')['default']
    LanguageSwitch: typeof import('./src/components/global/LanguageSwitch.vue')['default']
    LogoIcon: typeof import('./src/components/icons/LogoIcon.vue')['default']
    ProxySettings: typeof import('./src/components/rightMenu/components/proxySettings.vue')['default']
    QuickReply: typeof import('./src/components/rightMenu/components/quickReply.vue')['default']
    QuickReplyIcon: typeof import('./src/components/icons/QuickReplyIcon.vue')['default']
    ReplySetting: typeof import('./src/components/rightMenu/components/replySetting.vue')['default']
    ReplyUser: typeof import('./src/components/rightMenu/components/replyUser.vue')['default']
    RightMenu: typeof import('./src/components/rightMenu/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchFrom: typeof import('./src/components/accountBar/components/SearchFrom.vue')['default']
    SelectPage: typeof import('./src/components/SelectPage/index.vue')['default']
    ServerIcon: typeof import('./src/components/icons/ServerIcon.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TelegramIcon: typeof import('./src/components/icons/TelegramIcon.vue')['default']
    ThemeSwitch: typeof import('./src/components/global/ThemeSwitch.vue')['default']
    TiktikIcon: typeof import('./src/components/icons/TiktikIcon.vue')['default']
    TranslateIcon: typeof import('./src/components/icons/TranslateIcon.vue')['default']
    TranslateSetting: typeof import('./src/components/rightMenu/components/translateSetting.vue')['default']
    TranslateSettingIcon: typeof import('./src/components/icons/TranslateSettingIcon.vue')['default']
    TypeTab: typeof import('./src/components/accountBar/components/TypeTab.vue')['default']
    Update: typeof import('./src/components/update/index.vue')['default']
    UploadContact: typeof import('./src/components/contactBar/components/UploadContact.vue')['default']
    WhatsAppIcon: typeof import('./src/components/icons/WhatsAppIcon.vue')['default']
  }
}

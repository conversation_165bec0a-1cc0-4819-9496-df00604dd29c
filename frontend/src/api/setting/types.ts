export interface IPConfig {
    host: string //IP地址
    port: number|undefined //端口
    userId: string //用户名
    password: string //密码
    ipMold: string //IP协议类型 http/socks5

    [key: string]: any
}

// 翻译路线类型
export interface TranslateRoute {
    id: number
    chineseName: string
    englishName: string
    software: number
}

// 翻译语言类型
export interface TranslateLanguage {
    englishName: string
    chineseName: string
    languageCode: string
}
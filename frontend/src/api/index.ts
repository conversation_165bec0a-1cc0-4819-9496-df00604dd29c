/**
 * 主进程与渲染进程通信频道定义
 * Definition of communication channels between main process and rendering process
 */
export const ipcApiRoute = {
  getSystemInfo: 'controller/system/getBaseInfo',
  login: 'controller/system/login',
  logOut: 'controller/system/logOut',

  //session会话相关
  getSessions: 'controller/window/getSessions',
  getSessionByPartitionId: 'controller/window/getSessionByPartitionId',
  addSession: 'controller/window/addSession',
  editSession: 'controller/window/editSession',
  startSession: 'controller/window/startSession',
  setWindowLocation: 'controller/window/setWindowLocation',
  hiddenSession: 'controller/window/hiddenSession',
  showSession: 'controller/window/showSession',
  closeSession: 'controller/window/closeSession',
  refreshSession: 'controller/window/refreshSession',
  deleteSession: 'controller/window/deleteSession',
  // 代理配置相关
  getProxyInfo: 'controller/window/getProxyInfo',
  editProxyInfo: 'controller/window/editProxyInfo',
  saveProxyInfo: 'controller/window/saveProxyInfo',
  openSessionDevTools: 'controller/window/openSessionDevTools',

  //翻译相关
  getTrsConfig: 'controller/translate/getConfigInfo',
  updateTranslateConfig: 'controller/translate/updateTranslateConfig',
  changeAloneStatus: 'controller/translate/changeAloneStatus',

  getLanguageList: 'controller/translate/getLanguageList',
  addLanguage: 'controller/translate/addLanguage',
  deleteLanguage: 'controller/translate/deleteLanguage',
  editLanguage: 'controller/translate/editLanguage',
  addTranslateRoute: 'controller/translate/addTranslateRoute',
  editTranslateRoute: 'controller/translate/editTranslateRoute',
  getRouteConfig: 'controller/translate/getRouteConfig',
  getRouteList: 'controller/translate/getRouteList',
  testRoute: 'controller/translate/testRoute',
  translateText: 'controller/translate/translateText',
  clearSessionCache: 'controller/translate/clearSessionCache',

  //联系人信息相关
  getContactInfo: 'controller/contactInfo/getContactInfo',
  updateContactInfo: 'controller/contactInfo/updateContactInfo',

  getFollowRecord: 'controller/contactInfo/getFollowRecord',
  addFollowRecord: 'controller/contactInfo/addFollowRecord',
  updateFollowRecord: 'controller/contactInfo/updateFollowRecord',
  deleteFollowRecord: 'controller/contactInfo/deleteFollowRecord',

  //快捷回复相关
  getGroups: 'controller/quickreply/getGroups',
  getContentByGroupId: 'controller/quickreply/getContentByGroupId',
  addGroup: 'controller/quickreply/addGroup',
  editGroup: 'controller/quickreply/editGroup',
  deleteGroup: 'controller/quickreply/deleteGroup',

  addReply: 'controller/quickreply/addReply',
  editReply: 'controller/quickreply/editReply',
  deleteReply: 'controller/quickreply/deleteReply',
  deleteAllReply: 'controller/quickreply/deleteAllReply',

  //密钥管理相关
  generateAndStoreKeys: 'controller/keyManager/generateAndStoreKeys',
  validateAndRestoreKeys: 'controller/keyManager/validateAndRestoreKeys',
  checkKeyStatus: 'controller/keyManager/checkKeyStatus',
  clearAllKeys: 'controller/keyManager/clearAllKeys',
  encryptText: 'controller/keyManager/encryptText',
  decryptText: 'controller/keyManager/decryptText',

  //系统设置相关
  getSystemSettings: 'controller/system/getSystemSettings',
  updateSystemSettings: 'controller/system/updateSystemSettings',
  selectCacheDirectory: 'controller/system/selectCacheDirectory',
  getCacheDirectory: 'controller/system/getCacheDirectory',
  getCacheInfo: 'controller/system/getCacheInfo',
  clearCache: 'controller/system/clearCache',
  clearDatabaseCache: 'controller/system/clearDatabaseCache',
  clearAllCache: 'controller/system/clearAllCache',

  /**
   * HTTP请求代理
   */
  httpRequest: 'controller/system/httpRequest',

  /**
   * 本地代理连接测试
   */
  testProxyConnection: 'controller/system/testProxyConnection'
} as const;

import ipcRequest from '../../utils/ipcRequest';
import type { LoginResponse, GetInfoResponse } from './types';

// 获取线路列表 - 使用IPC代理避免CORS问题
export function apiLineList() {
    return ipcRequest({
        url: '/auth/ping',
        method: 'get'
    });
}

// 登录
export function login(data: any): Promise<LoginResponse> {
    return ipcRequest({
        url: '/auth/client/login',
        method: 'post',
        data
    });
}

// 刷新token
export function refreshToken(data: any) {
    return ipcRequest({
        url: '/auth/client/refresh-token',
        method: 'post',
        data
    });
}

// 获取用户信息
export function getInfo(): Promise<GetInfoResponse> {
    return ipcRequest({
        url: '/system/user/getInfo',
        method: 'get',
    });
}

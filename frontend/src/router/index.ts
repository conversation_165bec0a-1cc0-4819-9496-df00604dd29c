import { createRouter, createWebHashHistory } from 'vue-router';
import routerMap from './routerMap';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { useMenuStore } from '@/stores/menuStore';

const Router = createRouter({
  history: createWebHashHistory(),
  routes: routerMap
});

// 添加路由历史控制
let isNavigatingBack = false;

Router.beforeEach(async (to, from, next) => {
  // 如果是浏览器后退触发的导航，且从 dashboard 页面后退
  if (isNavigatingBack && from.path === '/dashboard') {
    isNavigatingBack = false;
    next(false); // 取消导航
    return;
  }

  // 原有的路由守卫逻辑
  if (from.path === '/dashboard' && (to.path === '/login' || to.path === '/')) {
    await ipc.invoke(ipcApiRoute.hiddenSession, {});
    const menuStore = useMenuStore();
    menuStore.setCurrentMenu('Home');
    menuStore.setRightFoldStatus(false);
  }
  next();
});

// 监听浏览器后退事件
window.addEventListener('popstate', () => {
  isNavigatingBack = true;
});

// 禁用鼠标侧键
window.addEventListener(
  'mouseup',
  (e: MouseEvent) => {
    if (e.button === 3 || e.button === 4) {
      // 鼠标侧键
      e.preventDefault();
      e.stopPropagation();
    }
  },
  true
);

export default Router;

import { RouteRecordRaw } from 'vue-router';
import MainLayout from '@/layout/MainLayout.vue';

/**
 * 基础路由
 */
const constantRouterMap: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '',
    component: MainLayout,
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'dashboard',
        component: () => import('@/views/index/index.vue')
      },
      {
        path: '/whatsapp',
        name: 'whatsApp',
        component: () => import('@/views/whatsApp/index.vue')
      },
      {
        path: '/telegram',
        name: 'telegram',
        component: () => import('@/views/telegram/index.vue')
      },
      {
        path: '/settings',
        name: 'settings',
        component: () => import('@/views/settings/index.vue')
      }
    ]
  }
];

export default constantRouterMap;

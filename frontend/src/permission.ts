import router from './router';
import { useTitle } from '@vueuse/core';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

// 配置NProgress
NProgress.configure({
  showSpinner: false, // 是否显示加载微调器
  easing: 'ease', // 动画方式
  speed: 500, // 动画速度
  minimum: 0.3 // 初始显示百分比
});

// 白名单路由，不需要登录即可访问
const whiteList = ['/login'];

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  // 开始进度条
  NProgress.start();

  // 设置页面标题
  if (to.meta.title) {
    useTitle(`${to.meta.title}`);
  }

  // 获取token和自动登录状态
  const hasToken = localStorage.getItem('access_token');
  const autoLogin = localStorage.getItem('autoLogin');
  const savedKey = localStorage.getItem('savedAuthKey');

  // 判断是否需要登录
  if (hasToken) {
    // 有token，说明已登录
    if (to.path === '/login') {
      // 如果访问登录页，重定向到首页
      next({ path: '/' });
      NProgress.done();
    } else {
      // 正常访问其他页面
      next();
    }
  } else {
    // 没有token（未登录状态）
    if (whiteList.includes(to.path)) {
      // 如果访问的是白名单页面（如登录页）

      // 检查是否启用了自动登录且有保存的密钥
      if (autoLogin === 'true' && savedKey && to.path === '/login') {
        try {
          // 尝试使用保存的密钥自动登录
          const { ipc } = await import('@/utils/ipcRenderer');
          const { ipcApiRoute } = await import('@/api');
          const { useUserInfoStore } = await import('@/stores/modules/userInfo');

          const loginResult = await ipc.invoke(ipcApiRoute.login, {
            authKey: savedKey
          });

          if (loginResult.status) {
            // 自动登录成功，保存用户信息并跳转到首页
            const userInfo = loginResult.data;
            localStorage.setItem('access_token', userInfo.authKey);
            localStorage.setItem('userInfo', JSON.stringify(userInfo));

            // 初始化用户信息和代理配置
            const userStore = useUserInfoStore();
            await userStore.initUserInfo();

            console.log('自动登录成功，跳转到首页');
            next({ path: '/' });
            NProgress.done();
            return;
          } else {
            // 自动登录失败，清除相关状态，继续到登录页
            console.log('自动登录失败:', loginResult.message);
            localStorage.removeItem('autoLogin');
            localStorage.removeItem('savedAuthKey');
            localStorage.removeItem('keyAccount');
          }
        } catch (error) {
          console.error('自动登录过程出错:', error);
          // 出错时清除相关状态
          localStorage.removeItem('autoLogin');
          localStorage.removeItem('savedAuthKey');
          localStorage.removeItem('keyAccount');
        }
      }

      // 正常进入登录页或其他白名单页面
      next();
    } else {
      // 如果访问的不是白名单页面，则重定向到登录页
      // 将原本要访问的路径作为参数传递，便于登录后跳转回原页面
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
      // 当重定向时手动结束进度条
      NProgress.done();
    }
  }
});

// 全局后置钩子
router.afterEach(() => {
  // 结束进度条
  NProgress.done();
});

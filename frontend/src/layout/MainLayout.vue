<template>
  <el-container class="h-screen">
    <!-- 自定义标题栏 - 固定在顶部 -->
    <el-header class="title-bar-height">
      <TitleBar show-center show-left />
    </el-header>

    <!-- 主体布局 - 占据剩余空间 -->
    <el-container class="main-content-height">
      <!-- 左侧导航栏 - 固定宽度，不滚动 -->
      <el-aside :width="sidebarCollapsed ? '64px' : '64px'" class="bg-white side-nav-full-height">
        <SideNav :collapsed="sidebarCollapsed" />
      </el-aside>

      <!-- 主要内容区域 - 可滚动 -->
      <el-main class="bg-gray-50 content-scroll-area">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts" setup>
import TitleBar from './components/TitleBar.vue';
import SideNav from './components/SideNav.vue';

// 侧边栏折叠状态
const sidebarCollapsed = ref(false);

// 响应式处理 - 小屏幕自动折叠
const handleResize = () => {
  sidebarCollapsed.value = window.innerWidth < 1024;
};

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
// Element Plus 容器样式重置
.el-container {
  height: 100vh;
}

.el-header {
  padding: 0;
  height: 32px !important;
  line-height: 32px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
  background-color: var(--el-bg-color);
}

.el-aside {
  padding: 0;
  height: 100% !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  z-index: 99;
  background-color: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
}

.el-main {
  padding: 0;
  height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  background-color: var(--el-bg-color-page);

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 主要内容区域高度
.main-content-height {
  height: calc(100vh - 32px) !important;
}

// 工具类
.h-screen {
  height: 100vh;
}

.bg-white {
  background-color: var(--el-bg-color);
}

.bg-gray-50 {
  background-color: var(--el-bg-color-page);
}

// 确保内容区域内的padding div不影响滚动
.content-scroll-area {
  height: calc(100vh - 32px);
}
</style>

<template>
  <div :style="{ background: transparent ? 'transparent' : 'white', padding: '0' }" class="title-bar h-8 border-b border-gray-300 flex items-center select-none relative z-1000">
    <!-- 可拖拽区域 -->
    <div :class="{ 'bg-#10B981': !transparent }" class="w-full flex items-center justify-between px-4 drag-region" style="-webkit-app-region: drag">
      <!-- 左侧应用信息 -->
      <TitleBarLeft v-if="showLeft" :dark="darkButtons" class="no-drag" style="-webkit-app-region: no-drag" />
      <div v-else class="flex-1"></div>

      <!-- 中间区域 -->
      <TitleBarCenter v-if="showCenter" />

      <!-- 右侧窗口控制按钮 -->
      <TitleBarRight :dark="darkButtons" class="no-drag" style="-webkit-app-region: no-drag" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import TitleBarLeft from './TitleBarLeft.vue';
import TitleBarCenter from './TitleBarCenter.vue';
import TitleBarRight from './TitleBarRight.vue';

// 定义props
defineProps<{
  showLeft?: boolean;
  showCenter?: boolean;
  darkButtons?: boolean;
  transparent?: boolean;
}>();
</script>

<style scoped>
/* 拖拽区域 - UnoCSS 不支持 webkit-app-region */
.drag-region {
  -webkit-app-region: drag;
  cursor: move;
  /* 确保拖拽区域有足够的高度 */
  min-height: 32px;
}

/* 禁用拖拽的区域（按钮等交互元素） */
.no-drag {
  -webkit-app-region: no-drag;
  cursor: default;
}

/* macOS 特定样式 */
@media (platform: darwin) {
  .drag-region {
    min-height: 28px;
  }
}

/* Windows 特定样式 */
@media (platform: win32) {
  .drag-region {
    min-height: 32px;
  }
}

/* 确保拖拽区域覆盖整个标题栏 */
.title-bar {
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  /* 确保标题栏在最顶层 */
  z-index: 1000;
  /* 防止内容溢出 */
  overflow: hidden;
}

/* 防止文本选择干扰拖拽 */
.title-bar * {
  user-select: none;
  -webkit-user-select: none;
}

/* 确保按钮等交互元素可以正常点击 */
.no-drag,
.no-drag * {
  -webkit-app-region: no-drag !important;
  cursor: default;
}

/* 确保子元素不会覆盖拖拽区域 */
.title-bar > div {
  pointer-events: auto;
}
</style>

<template>
  <div class="flex items-center gap-2 text-xs text-gray-700 no-drag">
    <el-text :class="{ '!text-white': !dark }" class="font-medium text-xs">{{ title }}</el-text>
  </div>
</template>

<script lang="ts" setup>
// 定义props
defineProps({
  dark: {
    type: Boolean,
    default: false
  }
});
const title = import.meta.env.VITE_APP_TITLE;
</script>

<style scoped>
.no-drag {
  -webkit-app-region: no-drag;
}
</style>

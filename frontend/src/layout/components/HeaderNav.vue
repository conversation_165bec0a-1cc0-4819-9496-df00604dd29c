<template>
  <div class="w-full flex bg-#10B981 items-center justify-between">
    <!-- 左侧区域 -->
    <div class="flex items-center space-x-4">
      <!-- Logo和标题 -->
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-sm">AC</span>
        </div>
        <div class="hidden sm:block">
          <h1 class="text-lg font-semibold text-gray-900">{{ $t('layout.title') }}</h1>
          <p class="text-xs text-gray-500">{{ $t('layout.subtitle') }}</p>
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="flex items-center space-x-4">
      <!-- 语言切换 -->
      <el-dropdown @command="handleLanguageChange">
        <el-button circle text>
          <span class="text-base">🌐</span>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="zh">
              <span class="text-base mr-2">🇨🇳</span>
              简体中文
            </el-dropdown-item>
            <el-dropdown-item command="en">
              <span class="text-base mr-2">🇺🇸</span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 通知 -->
      <el-badge :max="99" :value="3">
        <el-button circle text>
          <span class="text-base">🔔</span>
        </el-button>
      </el-badge>

      <!-- 用户信息 -->
      <el-dropdown @command="handleUserAction">
        <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
          <div class="hidden sm:block text-sm">
            <div class="font-medium text-gray-900">WS001</div>
            <div class="text-xs text-gray-500">专业版权限</div>
          </div>
          <span class="text-xs text-gray-400">▼</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <span class="text-base mr-2">👤</span>
              {{ t('user.profile') }}
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <span class="text-base mr-2">⚙️</span>
              {{ t('user.settings') }}
            </el-dropdown-item>
            <el-dropdown-item command="logout" divided>
              <span class="text-base mr-2">🚪</span>
              {{ t('user.logout') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n';

// 定义事件
defineEmits(['toggle-sidebar']);

const { t, locale } = useI18n();

// 语言切换
const handleLanguageChange = (key: string) => {
  locale.value = key;
};

// 用户操作
const handleUserAction = (key: string) => {
  switch (key) {
    case 'profile':
      console.log('打开个人资料');
      break;
    case 'settings':
      console.log('打开设置');
      break;
    case 'logout':
      console.log('退出登录');
      break;
  }
};
</script>

<style lang="scss" scoped>
.el-badge {
  line-height: 1;
}

.el-button {
  border: none;
  background: transparent;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>

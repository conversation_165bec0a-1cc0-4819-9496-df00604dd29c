<template>
  <div class="flex items-center gap-px no-drag">
    <!-- 最小化按钮 -->
    <el-button :title="$t('window.minimize')" :class="['window-control-btn', dark ? 'title-button-dark' : 'title-button']" text size="small" @click="handleMinimize">
      <svg height="12" viewBox="0 0 12 12" width="12">
        <path d="M2,6 L10,6" stroke="currentColor" stroke-linecap="round" stroke-width="1.5" />
      </svg>
    </el-button>

    <!-- 最大化/还原按钮 -->
    <el-button :title="isMaximized ? $t('window.restore') : $t('window.maximize')" :class="['window-control-btn', dark ? 'title-button-dark' : 'title-button']" text size="small" @click="handleMaximize">
      <svg height="12" viewBox="0 0 12 12" width="12">
        <!-- 最大化图标 -->
        <path v-if="!isMaximized" d="M2,2 L10,2 L10,10 L2,10 Z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="1.5" />
        <!-- 还原图标 -->
        <g v-else>
          <path d="M3,3 L9,3 L9,9 L3,9 Z" fill="none" stroke="currentColor" stroke-width="1.5" />
          <path d="M5,1 L10,1 L10,6 M8,3 L10,1" fill="none" stroke="currentColor" stroke-width="1.5" />
        </g>
      </svg>
    </el-button>

    <!-- 关闭按钮 -->
    <el-button :title="$t('window.close')" :class="['window-control-btn close-btn', dark ? 'title-button-dark' : 'title-button']" text size="small" @click="handleClose">
      <svg height="12" viewBox="0 0 12 12" width="12">
        <path d="M2,2 L10,10 M10,2 L2,10" stroke="currentColor" stroke-linecap="round" stroke-width="1.5" />
      </svg>
    </el-button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { ipc } from '@/utils/ipcRenderer';

// 定义props
defineProps({
  dark: {
    type: Boolean,
    default: false
  }
});

// 窗口状态
const isMaximized = ref(false);

// 窗口控制方法
const handleMinimize = async () => {
  try {
    await ipc.send('window-control', { action: 'minimize' });
  } catch (error) {
    console.error('最小化窗口失败:', error);
  }
};

const handleMaximize = async () => {
  try {
    await ipc.send('window-control', { action: 'toggle-maximize' });
    // 切换状态
    isMaximized.value = !isMaximized.value;
  } catch (error) {
    console.error('最大化/还原窗口失败:', error);
  }
};

const handleClose = async () => {
  try {
    await ipc.send('window-control', { action: 'close' });
  } catch (error) {
    console.error('关闭窗口失败:', error);
  }
};

// 初始化窗口状态
const initWindowState = async () => {
  try {
    // 这里可以添加获取初始窗口状态的逻辑
    // 由于当前的preload没有暴露获取状态的方法，我们先设置为false
    isMaximized.value = false;
  } catch (error) {
    console.error('初始化窗口状态失败:', error);
  }
};

// 监听窗口状态变化
const handleWindowStateChange = () => {
  // 这里可以添加监听窗口状态变化的逻辑
  // 目前先使用简单的状态切换
};

onMounted(() => {
  initWindowState();
  handleWindowStateChange();
});

onUnmounted(() => {
  // 清理事件监听
});
</script>

<style scoped>
.no-drag {
  -webkit-app-region: no-drag;
}

/* 窗口控制按钮基础样式 */
.window-control-btn {
  width: 32px !important;
  height: 32px !important;
  border-radius: 0 !important;
  border: none !important;
  padding: 0 !important;
  min-height: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

/* 亮色主题按钮样式 */
.title-button {
  color: white !important;
  background: transparent !important;
}

.title-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.title-button svg path,
.title-button svg g {
  stroke: white !important;
}

.title-button:hover svg path,
.title-button:hover svg g {
  stroke: white !important;
}

/* 暗色主题按钮样式 */
.title-button-dark {
  color: #666666 !important;
  background: transparent !important;
}

.title-button-dark:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.title-button-dark svg path,
.title-button-dark svg g {
  stroke: #666666 !important;
}

.title-button-dark:hover svg path,
.title-button-dark:hover svg g {
  stroke: #333333 !important;
}

/* 关闭按钮特殊样式 */
.close-btn:hover {
  background-color: #e81123 !important;
  color: white !important;
}

.close-btn:hover svg path {
  stroke: white !important;
}

.title-button-dark.close-btn:hover {
  background-color: #e81123 !important;
  color: white !important;
}

.title-button-dark.close-btn:hover svg path {
  stroke: white !important;
}

/* 移除Element Plus默认样式 */
:deep(.el-button) {
  border: none !important;
  box-shadow: none !important;
}

:deep(.el-button:focus) {
  outline: none !important;
  box-shadow: none !important;
}

:deep(.el-button:active) {
  transform: none !important;
}
</style>

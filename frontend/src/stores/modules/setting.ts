import { defineStore } from 'pinia'
import { useStorage } from '@vueuse/core'
import { ChatType } from '@/enum/ChatType'

interface ProxyConfig {
  enabled: boolean
  type: 'http' | 'socks5'
  host: string
  port: number
  username?: string
  password?: string
}

interface TranslationConfig {
  enabled: boolean
  provider: 'google' | 'baidu' | 'deepl'
  apiKey?: string
  autoTranslate: boolean
  targetLanguage: string
  sourceLanguage: string
}

interface QuickReply {
  id: string
  title: string
  content: string
  shortcut?: string
  chatTypes: ChatType[]
}

interface CustomerProfile {
  showAvatar: boolean
  showNickname: boolean
  showPhone: boolean
  showEmail: boolean
  showTags: boolean
  showNotes: boolean
  autoSaveNotes: boolean
}

export const useSettingStore = defineStore('setting', () => {
  // 当前聊天类型
  const currentChatType = useStorage('current-chat-type', ref<ChatType>())
  
  // 账号代理设置 - 按聊天类型分别配置
  const proxySettings = useStorage('proxy-settings', ref<Record<ChatType, ProxyConfig>>({
    [ChatType.WHATSAPP]: {
      enabled: false,
      type: 'http',
      host: '',
      port: 8080
    },
    [ChatType.TELEGRAM]: {
      enabled: false,
      type: 'socks5',
      host: '',
      port: 1080
    }
  }))

  // 翻译设置
  const translationSettings = useStorage('translation-settings', ref<TranslationConfig>({
    enabled: false,
    provider: 'google',
    autoTranslate: false,
    targetLanguage: 'zh-CN',
    sourceLanguage: 'auto'
  }))

  // 快捷回复
  const quickReplies = useStorage('quick-replies', ref<QuickReply[]>([
    {
      id: '1',
      title: '欢迎语',
      content: '您好，很高兴为您服务！',
      shortcut: 'welcome',
      chatTypes: [ChatType.WHATSAPP, ChatType.TELEGRAM]
    }
  ]))

  // 客户资料显示设置
  const customerProfileSettings = useStorage('customer-profile-settings', ref<CustomerProfile>({
    showAvatar: true,
    showNickname: true,
    showPhone: true,
    showEmail: true,
    showTags: true,
    showNotes: true,
    autoSaveNotes: true
  }))

  // 通用设置
  const generalSettings = useStorage('general-settings', ref({
    theme: 'light',
    language: 'zh-CN',
    fontSize: 14,
    autoLogin: true,
    enableNotifications: true,
    soundEnabled: true,
    showOnlineStatus: true
  }))

  // 获取当前聊天类型的代理设置
  const getCurrentProxyConfig = computed(() => {
    return currentChatType.value ? proxySettings.value[currentChatType.value] : null
  })

  // 更新代理设置
  const updateProxyConfig = (chatType: ChatType, config: ProxyConfig) => {
    proxySettings.value[chatType] = config
  }

  // 添加快捷回复
  const addQuickReply = (reply: Omit<QuickReply, 'id'>) => {
    const newReply: QuickReply = {
      ...reply,
      id: Date.now().toString()
    }
    quickReplies.value.push(newReply)
  }

  // 删除快捷回复
  const removeQuickReply = (id: string) => {
    const index = quickReplies.value.findIndex(reply => reply.id === id)
    if (index > -1) {
      quickReplies.value.splice(index, 1)
    }
  }

  // 获取指定聊天类型的快捷回复
  const getQuickRepliesByChatType = (chatType: ChatType) => {
    return quickReplies.value.filter(reply => 
      reply.chatTypes.includes(chatType)
    )
  }

  return {
    currentChatType,
    proxySettings,
    translationSettings,
    quickReplies,
    customerProfileSettings,
    generalSettings,
    getCurrentProxyConfig,
    updateProxyConfig,
    addQuickReply,
    removeQuickReply,
    getQuickRepliesByChatType
  }
})

import { defineStore } from 'pinia';
import { useStorage } from '@vueuse/core';
import type { Chat } from '@/types/chat';
import { useAccountStore } from '@/stores/chat/accounts';
import { useContactStore } from '@/stores/chat/contacts';
import { useGroupStore } from '@/stores/chat/groups';

export const useChatStore = defineStore('chat', () => {
  const accountStore = useAccountStore();
  const contactStore = useContactStore();
  const groupStore = useGroupStore();
  // 获取选中的账号
  const { currentAccount } = storeToRefs(accountStore);
  // 获取选中的联系人
  const { currentContact } = storeToRefs(contactStore);
  // 获取选中的群组
  const { currentGroup } = storeToRefs(groupStore);
  // 变量定义
  const currentChat = useStorage<Chat>('currentChat', null);

  // 方法定义
  const setCurrentChat = (chat: any) => {
    currentChat.value = chat;
  };

  // 返回值
  return {
    currentChat,
    setCurrentChat
  };
});

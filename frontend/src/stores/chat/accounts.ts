/**
 * 账号相关的状态管理
 * @remarks
 * 用于管理账号列表、当前选中账号等状态。
 * 包含以下功能:
 * - 管理账号列表数据
 * - 维护当前选中的账号信息
 * - 控制账号列表边栏的展开/折叠状态
 * 
 * @property isFold - 控制账号列表边栏是否折叠
 * @property accountScrollbar - 账号列表滚动条
 * @property apiVirtualAccount - WhatsApp API账号虚拟占位
 * @property currentAccount - 当前选中的账号信息
 * @property accountList - 所有账号列表数据
 * @property createBlankAccount - 创建账号占位
 * 
 * @example
 * ```ts
 * const accountStore = useAccountStore();
 * accountStore.isFold = true;
 * accountStore.currentAccount = { id: '1', account: 'test', accountType: 1, onlineStatus: 1 };
 * accountStore.accountList = [{ id: '1', account: 'test', accountType: 1, onlineStatus: 1 }];
 * ```
 */

import { defineStore } from 'pinia';
import { useStorage } from '@vueuse/core';
import { getRandomUUID } from '@/utils';
import type { Account } from '@/types/account';
import { ChatType } from '@/enum/ChatType';

export const useAccountStore = defineStore('accountBar', () => {
  const isFold = ref(false);
  const accountScrollbar = ref(null);
  const apiVirtualAccount = computed<Account>(()=>{
    // 只有 whatsapp 账号显示
    if(currentAccount.value.chatType !== ChatType.WHATSAPP) {
      return undefined;
    }
    return {
      id: -1,
      account: 'API虚拟账号',
      accountType: 1,
      onlineStatus: 1,
      chatType: ChatType.WHATSAPP
    }
  });

  const currentAccount = ref<Account>({
    id: '',
    account: '',
    accountType: 1,
    onlineStatus: 1
  });

  // 创建账号占位
  const createBlankAccount = async (chatType: ChatType) => {
    const account: Account = {
      id: getRandomUUID(),
      account: '',
      accountType: 2,
      onlineStatus: 1,
      chatType: chatType
    };
    accountList.value.unshift(account);
    goToTop();
  };

  const accountList = ref<Account[]>([
    // {
    //   id: 16556,
    //   loginEndpoint: 2,
    //   account: '*************',
    //   headImg: 'https://pub-1fc208598edc41debaecb57eb72b050e.r2.dev/HeadImg*************.png',
    //   nickname: null,
    //   useStatus: 1,
    //   seatAccount: '5125',
    //   onlineStatus: 5,
    //   reason: '账号已失效',
    //   tagIds: null,
    //   type: 2,
    //   country: null,
    //   accountEnvironment: null,
    //   registerTime: null,
    //   firstLoginTime: '2025-07-09 14:41:01',
    //   offlineTime: '2025-07-21 15:24:17',
    //   remark: null,
    //   topStatus: 0,
    //   isDel: 0,
    //   fileId: null,
    //   accountType: 2,
    //   importType: null,
    //   delTime: null,
    //   tags: null,
    //   totalUnReadNum: 582,
    //   sleepTime: null,
    //   nurturingCount: 0,
    //   broadcastCount: 1,
    //   broadResetTime: '2025-07-19 10:06:08',
    //   seatInfoVo: null
    // },
    // {
    //   id: 16555,
    //   loginEndpoint: 2,
    //   account: '************',
    //   headImg: 'https://pub-1fc208598edc41debaecb57eb72b050e.r2.dev/HeadImg************.png',
    //   nickname: null,
    //   useStatus: 1,
    //   seatAccount: '5125',
    //   onlineStatus: 1,
    //   reason: '',
    //   tagIds: null,
    //   type: 1,
    //   country: null,
    //   accountEnvironment: null,
    //   registerTime: null,
    //   firstLoginTime: '2025-07-08 14:52:29',
    //   offlineTime: '2025-07-21 14:20:00',
    //   remark: null,
    //   topStatus: 0,
    //   isDel: 0,
    //   fileId: null,
    //   accountType: 1,
    //   importType: null,
    //   delTime: null,
    //   tags: null,
    //   totalUnReadNum: 0,
    //   sleepTime: null,
    //   nurturingCount: 0,
    //   broadcastCount: 2,
    //   broadResetTime: '2025-07-14 16:53:22',
    //   seatInfoVo: null
    // }
  ]);

  // 滚动到顶部
  const goToTop = () => {
    setTimeout(() => {
      accountScrollbar.value.setScrollTop(0);
    }, 100);
  };

  // 滚动到指定位置
  const goToPosition = (position: number) => {
    setTimeout(() => {
      accountScrollbar.value.setScrollTop(position);
    }, 100);
  };

  // 滚动到指定账号
  const goToAccount = (account: Account) => {
    const index = accountList.value.findIndex(item => item.id === account.id);
    goToPosition(index * 50);
  };

  // 滚动到底部
  const goToBottom = () => {
    setTimeout(() => {
      accountScrollbar.value.setScrollTop(accountScrollbar.value.getScrollTop() + accountScrollbar.value.getScrollHeight());
    }, 100);
  };
  
  return {
    isFold,
    accountScrollbar,
    currentAccount,
    accountList,
    apiVirtualAccount,
    createBlankAccount,
  };
});
import { defineStore } from 'pinia';
import whatsapp from '@/components/icons/WhatsAppIcon.vue';
import tiktok from '@/components/icons/TiktikIcon.vue';
import telegram from '@/components/icons/TelegramIcon.vue';
import google from '@/components/icons/GoogleIcon.vue';
import { markRaw, Component } from 'vue';

// 类型定义
interface LocationInfo {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ChildMenu {
  id: string;
  platform: string;
  partitionId: string;
  title?: string;
  [key: string]: any;
}

interface Menu {
  id: string;
  title: string;
  icon: Component;
  color: string;
  openChildren?: boolean;
  children: ChildMenu[];
}

interface UserInfo {
  [key: string]: any;
}

interface MenuState {
  currentMenu: string;
  platform: string;
  currentPartitionId: string;
  locationInfo: LocationInfo;
  menus: Menu[];
  translationRoute: any[];
  isChildMenu: boolean;
  rightContent: string;
  rightFoldStatus: boolean;
  userInfo: UserInfo;
}

export const useMenuStore = defineStore('platform', {
  state: (): MenuState => ({
    currentMenu: 'Home',
    platform: '',
    currentPartitionId: '',
    locationInfo: {
      x: 0,
      y: 0,
      width: 0,
      height: 0
    },
    menus: [
      {
        id: 'WhatsApp',
        title: 'WhatsApp',
        icon: markRaw(whatsapp),
        color: 'white',
        openChildren: false,
        children: []
      },
      {
        id: 'Telegram',
        title: 'Telegram',
        icon: markRaw(telegram),
        color: '#30ace1',
        openChildren: false,
        children: []
      },
      {
        id: 'TikTok',
        title: 'TikTok',
        icon: markRaw(tiktok),
        color: 'white',
        openChildren: false,
        children: []
      },
      {
        id: 'CustomWeb',
        title: 'menu.customWeb',
        color: '#30ace1',
        icon: markRaw(google),
        children: []
      }
    ],
    // 可用的翻译平台组件
    translationRoute: [],
    isChildMenu: false,
    rightContent: 'TranslateConfig',
    rightFoldStatus: false,
    userInfo: {}
  }),
  actions: {
    setUserInfo(user: UserInfo) {
      this.userInfo = user;
    },
    setTranslationRoute(list: any[]) {
      this.translationRoute = list;
    },
    setCurrentMenu(menuName: string) {
      this.currentMenu = menuName;
    },
    setCurrentPartitionId(partitionId: string) {
      this.currentPartitionId = partitionId;
    },
    setCurrentPlatform(platform: string) {
      this.platform = platform;
    },
    setLocationInfo(info: LocationInfo) {
      this.locationInfo = info;
    },
    addChildrenMenu(childMenu: ChildMenu) {
      const pMenu = this.menus.find((item) => item.id === childMenu.platform);
      if (pMenu) {
        const oldMenu = pMenu.children.find((item) => item.partitionId === childMenu.partitionId);
        if (!oldMenu) {
          pMenu.children.push(childMenu);
        }
      }
    },
    updateChildrenMenu(childMenu: ChildMenu) {
      const pMenu = this.menus.find((item) => item.id === childMenu?.platform);
      if (pMenu) {
        const index = pMenu.children.findIndex((item) => item.partitionId === childMenu.partitionId);
        if (index !== -1) {
          pMenu.children.splice(index, 1, childMenu);
        }
      }
    },
    deleteChildrenMenu(childMenu: ChildMenu) {
      const pMenu = this.menus.find((item) => item.id === childMenu.platform);
      if (pMenu) {
        const dMenu = pMenu.children.find((item) => item.partitionId === childMenu.partitionId);
        if (dMenu) {
          const index = pMenu.children.findIndex((item) => item.partitionId === childMenu.partitionId);
          if (index !== -1) {
            pMenu.children.splice(index, 1);
          }
        }
      }
    },
    setIsChildMenu(partitionId: string) {
      if ('MoreSetting' === partitionId || 'QuickReply' === partitionId || 'Home' === partitionId || 'TranslateConfig' === partitionId) {
        this.isChildMenu = false;
        return;
      }
      const pMenu = this.menus.find((item) => item.id === partitionId);
      this.isChildMenu = !pMenu;
    },
    setRightContent(name: string) {
      this.rightContent = name;
    },
    setRightFoldStatus(status: boolean) {
      this.rightFoldStatus = status;
    },
    setMenuChildren(menuName: string, childArr: ChildMenu[]) {
      const menu = this.menus.find((item) => item.id === menuName);
      if (!menu || !childArr?.length) return; // 空值检查

      // 创建已有子项ID的快速查找集合
      const existingIds = new Set(menu.children.map((child) => child.id));

      // 过滤出需要添加的新项
      const newItems = childArr.filter(
        (newItem) => !existingIds.has(newItem.id) // O(1)时间复杂度查找
      );

      if (newItems.length > 0) {
        // 使用响应式数组更新（Vue/React等框架需要）
        menu.children = [...menu.children, ...newItems];

        // 或者直接修改原数组（非响应式场景）
        // menu.children.push(...newItems);
      }
    }
  },
  getters: {
    getCurrentMenu(state): string {
      return state.currentMenu;
    },
    getLocationInfo(state): LocationInfo {
      return state.locationInfo;
    },
    getMenuById:
      (state) =>
      (id: string): Menu | undefined => {
        return state.menus.find((menu) => menu.id === id);
      },
    getMenus: (state) => (): Menu[] => {
      return state.menus;
    },
    getCurrentChildren: (state) => (): ChildMenu[] => {
      const menu = state.menus.find((menu) => menu.id === state.currentMenu);
      return menu?.children || [];
    },
    getParentMenuById:
      (state) =>
      (id: string): Menu | null => {
        for (const menu of state.menus) {
          if (menu.children && menu.children.some((child) => child.partitionId === id)) {
            return menu;
          }
        }
        return null;
      },
    getIsChildMenu: (state) => (): boolean => {
      return state.isChildMenu;
    }
  }
});

<script setup>
import { useI18n } from 'vue-i18n';
import { ref, computed } from 'vue';

// 定义props
const props = defineProps({
  size: {
    type: Number,
    default: 32
  }
});

const { locale } = useI18n();
const currentLanguage = ref(locale.value);

// 计算样式
const switchStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`
}));

const fontSize = computed(() => ({
  fontSize: `${Math.max(props.size * 0.4375, 12)}px` // 保持字体大小为容器大小的0.4375倍，最小12px
}));

const handleLanguageChange = (lang) => {
  locale.value = lang;
  currentLanguage.value = lang;
};
</script>

<template>
  <div class="action-item language-switch">
    <el-dropdown @command="handleLanguageChange" trigger="click">
      <div class="switch-content" :style="switchStyle">
        <span class="language-text" :style="fontSize">
          {{ currentLanguage === 'zh' ? '中' : currentLanguage === 'en' ? 'En' : 'ខ្មែរ' }}
        </span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="zh">中文</el-dropdown-item>
          <el-dropdown-item command="en">English</el-dropdown-item>
          <el-dropdown-item command="km">ខ្មែរ</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style scoped>
.language-switch {
  display: flex;
  align-items: center;
  padding: 0;
  transition: all 0.3s;
}

.switch-content {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: var(--el-fill-color-light);
  border-radius: 50%;
  transition: all 0.3s;
}

.switch-content:hover {
  transform: translateY(-1px);
  background: var(--el-fill-color);
}

.language-text {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

:deep(.el-dropdown-menu) {
  border-radius: 8px;
  padding: 4px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  min-width: 100px;
}

:deep(.el-dropdown-menu__item) {
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  text-align: center;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--el-fill-color-light);
  color: var(--el-color-primary);
}
</style>

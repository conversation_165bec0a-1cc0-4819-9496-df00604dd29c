<template>
  <el-dialog v-model="dialogVisible" :before-close="handleClose" :show-close="false" align="center" align-center width="500">
    <template #header>
      <span class="font-bold font-size-18px">版本更新</span>
    </template>
    <div class="mb9">A2C.chat客户端已发布新功能，是否立即更新？</div>
    <template #footer>
      <div class="dialog-footer text-center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 更新 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
const dialogVisible = ref(false);
const handleClose = () => {
  console.log('close');
};
const handleSubmit = () => {
  console.log('submit');
};
</script>
<style lang="scss" scoped></style>

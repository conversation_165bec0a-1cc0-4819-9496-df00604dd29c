<template>
  <div class="w-full h-full">
    <el-form ref="proxyFormRef" :model="proxyForm" class="proxy-form" label-position="left" label-width="60px">
      <el-form-item class="proxy-form-item">
        <div class="w-full flex items-center justify-between">
          <span>接收翻译设置</span>
          <el-switch v-model="proxyForm.enableProxy"/>
        </div>
      </el-form-item>

      <el-form-item label="翻译线路">
        <el-select v-model="proxyForm.protocol" placeholder="请选择翻译线路" style="width: 100%">
          <el-option label="HTTP" value="HTTP"></el-option>
          <el-option label="HTTPS" value="HTTPS"></el-option>
          <el-option label="SOCKS4" value="SOCKS4"></el-option>
          <el-option label="SOCKS5" value="SOCKS5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="目标语言">
        <el-select v-model="proxyForm.protocol" placeholder="请选择目标语言" style="width: 100%">
          <el-option label="HTTP" value="HTTP"></el-option>
          <el-option label="HTTPS" value="HTTPS"></el-option>
          <el-option label="SOCKS4" value="SOCKS4"></el-option>
          <el-option label="SOCKS5" value="SOCKS5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="proxy-form-item">
        <div class="w-full flex items-center justify-between">
          <span>发送翻译设置</span>
          <el-switch v-model="proxyForm.enableProxy"/>
        </div>
      </el-form-item>

      <el-form-item label="翻译线路">
        <el-select v-model="proxyForm.protocol" placeholder="请选择翻译线路" style="width: 100%">
          <el-option label="HTTP" value="HTTP"></el-option>
          <el-option label="HTTPS" value="HTTPS"></el-option>
          <el-option label="SOCKS4" value="SOCKS4"></el-option>
          <el-option label="SOCKS5" value="SOCKS5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="目标语言">
        <el-select v-model="proxyForm.protocol" placeholder="请选择目标语言" style="width: 100%">
          <el-option label="HTTP" value="HTTP"></el-option>
          <el-option label="HTTPS" value="HTTPS"></el-option>
          <el-option label="SOCKS4" value="SOCKS4"></el-option>
          <el-option label="SOCKS5" value="SOCKS5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="proxy-form-item">
        <div class="w-full flex items-center justify-between">
          <span>群组自动翻译</span>
          <el-switch v-model="proxyForm.enableProxy"/>
        </div>
      </el-form-item>

    </el-form>
  </div>
</template>
<script lang="ts" setup>
const proxyFormRef = ref<any>();
const proxyForm = ref({
  enableProxy: false,
  protocol: 'HTTP',
  host: '',
  port: '',
  enableAuth: false,
  username: '',
  password: '',
});
</script>
<style lang="scss" scoped>
.proxy-form-item {
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>

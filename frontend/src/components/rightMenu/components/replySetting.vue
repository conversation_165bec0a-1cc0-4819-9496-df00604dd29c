<template>
  <div class="w-full h-full">
    <el-form ref="proxyFormRef" :model="proxyForm" class="proxy-form" label-position="left" label-width="60px">
      <el-form-item class="proxy-form-item">
        <div class="w-full flex items-center justify-between">
          <span>好友通过自动回复</span>
          <el-switch v-model="proxyForm.enableProxy"/>
        </div>
      </el-form-item>

      <el-form-item class="proxy-form-item">
        <div class="w-full flex items-center justify-between">
          <span>关键词自动回复</span>
          <el-switch v-model="proxyForm.enableProxy"/>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script lang="ts" setup>
const proxyFormRef = ref<any>();
const proxyForm = ref({
  enableProxy: false,
  protocol: 'HTTP',
  host: '',
  port: '',
  enableAuth: false,
  username: '',
  password: '',
});
</script>
<style lang="scss" scoped>
.proxy-form-item {
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>

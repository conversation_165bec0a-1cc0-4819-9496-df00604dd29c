<template>
  <div class="w-full h-full flex flex-col justify-between items-center">
    <div class="w-full">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="主管代理" name="first">
          <el-form ref="form" :model="ruleForm" label-width="80px">
            <el-form-item label="选择登录IP">
              <el-select v-model="ruleForm.ip" placeholder="请选择登录IP">
                <el-option label="HTTP" value="first"></el-option>
                <el-option label="SOCKS4" value="second"></el-option>
                <el-option label="SOCKS5" value="third"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="自定义" name="second">
          <el-form ref="proxyFormRef" :model="proxyForm" class="proxy-form" label-position="left" label-width="60px">
            <el-form-item class="proxy-form-item">
              <div class="w-full flex items-center justify-between">
                <span>启用代理服务器</span>
                <el-switch v-model="proxyForm.enableProxy"/>
              </div>
            </el-form-item>

            <el-form-item label="代理协议">
              <el-select v-model="proxyForm.protocol" placeholder="请选择协议" style="width: 100%">
                <el-option label="HTTP" value="HTTP"></el-option>
                <el-option label="HTTPS" value="HTTPS"></el-option>
                <el-option label="SOCKS4" value="SOCKS4"></el-option>
                <el-option label="SOCKS5" value="SOCKS5"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="主机地址">
              <el-input
                  v-model="proxyForm.host"
                  :disabled="!proxyForm.enableProxy"
                  :rows="4"
                  placeholder="请输入主机地址"
                  type="textarea"
              />
            </el-form-item>

            <el-form-item label="端口号">
              <el-input
                  v-model="proxyForm.port"
                  :disabled="!proxyForm.enableProxy"
                  placeholder="请输入端口号"
              />
            </el-form-item>

            <el-form-item class="proxy-form-item">
              <div class="w-full flex items-center justify-between">
                <span>启用代理服务器验证</span>
                <el-switch v-model="proxyForm.enableAuth" :disabled="!proxyForm.enableProxy"/>
              </div>

            </el-form-item>

            <el-form-item label="用户名">
              <el-input
                  v-model="proxyForm.username"
                  :disabled="!proxyForm.enableProxy || !proxyForm.enableAuth"
                  placeholder="请输入用户名"
              />
            </el-form-item>

            <el-form-item label="密码">
              <el-input
                  v-model="proxyForm.password"
                  :disabled="!proxyForm.enableProxy || !proxyForm.enableAuth"
                  placeholder="请输入密码"
                  show-password
                  type="password"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="content-footer flex-center w-full height-40">
      <el-button type="primary" @click="onSubmit">应用</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>

const activeName = ref('first');

const proxyFormRef = ref()
const form = ref()
const ruleForm = ref({
  ip: '',
})
const proxyForm = ref({
  enableProxy: false,
  protocol: 'HTTP',
  host: '',
  port: '',
  enableAuth: false,
  username: '',
  password: ''
});

const handleClick = (tab, event) => {
  console.log(tab, event);
};

const onSubmit = () => {
  if (activeName.value === 'first') {
    console.log('主管代理设置:', ruleForm);
  } else if (activeName.value === 'second') {
    console.log('自定义代理设置:', proxyForm);
    // 这里可以添加表单验证和提交逻辑
    if (proxyForm.value.enableProxy && !proxyForm.value.host) {
      ElMessage.warning('请输入主机地址');
      return;
    }
    if (proxyForm.value.enableProxy && !proxyForm.value.port) {
      ElMessage.warning('请输入端口号');
      return;
    }
    if (proxyForm.value.enableAuth && (!proxyForm.value.username || !proxyForm.value.password)) {
      ElMessage.warning('请输入完整的验证信息');
      return;
    }
    ElMessage.success('代理设置已应用');
  }
}
</script>
<style lang="scss" scoped>
.content-footer {
  border-top: 1px solid #e5e7eb;
  @apply py-3.5
}


.proxy-form-item {
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>

<template>
  <div :class="'check-line-' + props.size" class="w-full min-w-220px flex items-center whitespace-nowrap gap-2">
    <el-select v-model="apiBaseUrl" :placeholder="t('login.lineSelector.placeholder')" remote style="border: none" @change="handleTypeChange" @dblclick="handleWriteLine">
      <template #label>
        <div class="flex items-center">
          <span v-if="props.size === 'small'" class="mr-1 scale-90">
            <!--            <svg-icon icon-class="ip"/>-->
          </span>
          <span :title="activeLine.url">{{ activeLine.name }}</span>
          <span v-if="activeLine.ms" :class="[activeLine.ms > 300 ? 'text-orange-500' : 'theme-color']">({{ activeLine.ms }}ms)</span>
          <el-button v-if="props.size !== 'small'" :disabled="isRefreshing" class="ml-1" link plain @click.stop="handleRefresh">
            <el-icon :class="{ 'animate-spin': isRefreshing }" size="16">
              <refresh />
            </el-icon>
          </el-button>
        </div>
      </template>
      <el-option v-for="item in sortedLineList" :key="item.url" :label="item.name" :value="item.url">
        <div class="text-13px">
          <span>{{ item.name }}</span>
          <span v-if="item.ms" :class="[item.ms > 300 ? 'text-orange-500' : 'theme-color']">({{ item.ms }}ms)</span>
        </div>
      </el-option>
    </el-select>
    <el-button v-if="props.size == 'small'" :disabled="isRefreshing" circle class="small-check-btn" plain type="info" @click.stop="handleRefresh">
      <el-icon :class="{ 'animate-spin': isRefreshing }" size="14">
        <refresh />
      </el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/stores/modules/app';
import { apiLineList } from '@/api/common';
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { PropType } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const appStore = useAppStore();
const { apiBaseUrl } = storeToRefs(appStore);
const activeLine = ref({
  name: appStore.apiLine.find((item) => item.url === apiBaseUrl.value)?.name || t('login.lineSelector.defaultLine'),
  url: apiBaseUrl.value,
  ms: 0
});
const props = defineProps({
  size: {
    type: String as PropType<'small' | 'default'>,
    default: 'default'
  }
});

const lineList = ref([]);

// 组件初始化时获取线路列表
onMounted(async () => {
  if (appStore.apiLine.length === 0) {
    await handleRefresh();
  }
  // 如果apiBaseUrl不在列表中，添加默认线路
  if (!appStore.apiLine.find((item) => item.url === appStore.apiBaseUrl)) {
    const defaultLine = {
      name: t('login.lineSelector.defaultLine'),
      url: appStore.apiBaseUrl,
      ms: 0
    };
    const newLineList = [...appStore.apiLine, defaultLine];
    appStore.updateApiLine(newLineList);
  }
  // 初始化线路列表
  lineList.value = appStore.apiLine;
});

// 切换线路
const handleTypeChange = (value: string) => {
  const selectedLine = lineList.value.find((line) => line.url === value);
  if (!selectedLine?.url) return;
  appStore.changeApiLine(value);
  // 更新选中线路
  activeLine.value = selectedLine;
};

// 是否刷新线路
const isRefreshing = ref(false);

// 线路按延迟时间排序
const sortedLineList = computed(() => {
  return [...lineList.value].sort((a, b) => (a.ms || 999) - (b.ms || 999));
});

// 检查线路延迟，超时控制
const checkLineDelay = async (url: string): Promise<number> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

  try {
    const start = Date.now();
    await fetch(url, {
      signal: controller.signal,
      method: 'HEAD' // 只获取头信息，减少数据传输
    });
    const delay = Date.now() - start;
    return delay;
  } catch (error) {
    return 999;
  } finally {
    clearTimeout(timeoutId);
  }
};

// 手动添加线路
const handleWriteLine = async () => {
  try {
    const { value } = await ElMessageBox.prompt(t('login.lineSelector.customLinePrompt'), t('login.lineSelector.customLineTitle'), {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      inputPlaceholder: t('login.lineSelector.inputPlaceholder'),
      inputPattern: /^https?:\/\/[^\s]+$/,
      inputErrorMessage: t('login.lineSelector.inputErrorMessage')
    });

    if (!value) return;

    // 检查是否已存在
    if (lineList.value.some((item) => item.url === value)) {
      ElMessage.warning(t('login.lineSelector.lineExists'));
      return;
    }

    // 检查新线路的延迟
    const ms = await checkLineDelay(value);
    const newLine = {
      name: value,
      url: value,
      ms
    };

    lineList.value = [...lineList.value, newLine];
    appStore.updateApiLine(lineList.value);
    ElMessage.success(t('login.lineSelector.addSuccess'));
  } catch (error) {
    // 用户取消操作时不显示错误
    if (error !== 'cancel') {
      ElMessage.error(t('login.lineSelector.addFailed'));
    }
  }
};

// 刷新获取线路
const emit = defineEmits(['refresh']);
const handleRefresh = async () => {
  if (isRefreshing.value) return;
  isRefreshing.value = true;
  emit('refresh');
  try {
    const res = await apiLineList();
    const ipList = (res as any).data;

    // 并发检测所有线路延迟
    const updatedIpList = await Promise.all(
      ipList.map(async (line) => {
        if (!line.url) return line;
        const ms = await checkLineDelay(line.url + (line.path || ''));
        return { ...line, ms };
      })
    );

    // 处理默认线路 - 不检测延迟
    const defaultLine = {
      name: t('login.lineSelector.defaultLine'),
      url: appStore.apiBaseUrl,
      ms: 0 // 默认线路不需要延迟
    };

    const finalList = updatedIpList.some((item) => item.url === appStore.apiBaseUrl) ? updatedIpList : [...updatedIpList, defaultLine];

    lineList.value = finalList;

    // 更新当前选中线路信息
    const currentLine = finalList.find((item) => item.url === appStore.apiBaseUrl);
    if (currentLine) {
      activeLine.value = { ...currentLine };
    }

    appStore.updateApiLine(finalList);
    ElMessage.success(t('login.lineSelector.refreshSuccess'));
  } catch (error) {
    ElMessage.error(t('login.lineSelector.refreshFailed'));
    console.error('刷新线路失败:', error);
  } finally {
    isRefreshing.value = false;
  }
};
</script>
<style lang="scss" scoped>
.check-line-default {
  // Element Plus 组件样式覆盖
  :deep(.el-select) {
    .el-select__wrapper {
      border-bottom: 1px solid #e5e7eb;
      background: #ffffff;
      box-shadow: none;
      transition: all 0.3s ease;
      padding: 0 12px;
      height: 48px;
    }
  }
}
</style>

<template>
  <svg t="1740503978968" class="icon" viewBox="0 0 1204 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1073" width="128" height="128">
    <path
      d="M90.352941 873.411765a60.235294 60.235294 0 0 0 60.235294 60.235294h632.470589a30.117647 30.117647 0 0 1 30.117647 30.117647v30.117647a30.117647 30.117647 0 0 1-30.117647 30.117647H120.470588a120.470588 120.470588 0 0 1-120.470588-120.470588V120.470588a120.470588 120.470588 0 0 1 120.470588-120.470588h783.058824a120.470588 120.470588 0 0 1 120.470588 120.470588v150.588236a30.117647 30.117647 0 0 1-30.117647 30.117647h-30.117647a30.117647 30.117647 0 0 1-30.117647-30.117647V150.588235a60.235294 60.235294 0 0 0-60.235294-60.235294H150.588235a60.235294 60.235294 0 0 0-60.235294 60.235294v722.82353z m843.294118-331.294118v-90.352941a30.117647 30.117647 0 0 1 30.117647-30.117647h30.117647a30.117647 30.117647 0 0 1 30.117647 30.117647v90.352941h150.588235a30.117647 30.117647 0 0 1 30.117647 30.117647v240.941177a30.117647 30.117647 0 0 1-30.117647 30.117647h-150.588235v150.588235a30.117647 30.117647 0 0 1-30.117647 30.117647h-30.117647a30.117647 30.117647 0 0 1-30.117647-30.117647v-150.588235h-150.588235a30.117647 30.117647 0 0 1-30.117648-30.117647v-240.941177a30.117647 30.117647 0 0 1 30.117648-30.117647h150.588235z m0 90.352941h-90.352941v120.470588h90.352941v-120.470588z m90.352941 0v120.470588h90.352941v-120.470588h-90.352941z m-613.496471-120.470588h82.522353L451.764706 376.470588 410.503529 512z m-27.497411 90.352941l-30.147765 99.117177a30.117647 30.117647 0 0 1-28.822588 21.353411H283.045647a30.117647 30.117647 0 0 1-28.521412-39.845647l143.841883-421.647058A30.117647 30.117647 0 0 1 426.857412 240.941176h49.814588a30.117647 30.117647 0 0 1 28.491294 20.389648l143.841882 421.647058A30.117647 30.117647 0 0 1 620.483765 722.823529h-40.990118a30.117647 30.117647 0 0 1-28.822588-21.353411L520.523294 602.352941h-137.517176z"
      :fill="color"
      p-id="1074"
    ></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>

<template>
  <svg t="1740396637742" class="icon" viewBox="0 0 1031 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3502" :width="size" :height="size">
    <path
      d="M515.894262 0.475997c163.911778 0 327.902246 0.393451 491.814025-0.472141 19.672561 0 24.157905 4.406654 24.079214 24.079214q-1.022973 487.879512 0 975.759025c0 19.672561-4.406654 24.157905-24.079214 24.157905q-491.814025-1.101663-983.628049 0c-19.672561 0-24.157905-4.485344-24.079215-24.157905q1.101663-487.879512 0-975.759025C0.001023 4.410509 4.407677-0.153525 24.080238 0.003856c163.911778 0.865593 327.902246 0.472141 491.814024 0.472141z m502.279827 512.194798A496.220678 496.220678 0 1 0 521.71734 1009.599685a496.378059 496.378059 0 0 0 496.456749-496.92889z"
      fill="#FBFBFB"
      p-id="3503"
    ></path>
    <path
      d="M1018.174089 512.670795a496.220678 496.220678 0 1 1-495.040324-495.748537 494.174732 494.174732 0 0 1 495.040324 495.748537zM409.662433 661.474046c1.888566-77.903341 25.416949-112.9205 85.221534-116.1468 24.708737-1.337734 27.935037-9.914971 26.518612-29.666222a327.351415 327.351415 0 0 1 0-43.043563c1.337734-19.987322-8.577237-25.416949-26.282541-22.66279a12.433059 12.433059 0 0 0-3.777132-2.596778c4.721415-21.797198-8.341166-22.820171-24.866117-23.056242-66.650637-0.786902-136.999715 44.696059-165.249512 108.041705a172.252944 172.252944 0 0 0 31.476097 192.712407c19.27911 26.754683 45.640341 44.932129 73.575378 61.14232 55.083171 38.55822 113.943473 34.859778 171.229971 8.419856 62.086602-28.564559 96.159478-80.421429 100.880893-148.33111 3.2263-47.214146 0.550832-94.428293 1.180354-141.642439 0-23.607073-6.924741-55.712693 5.193556-67.201468 13.770793-12.98389 37.063105 15.738049 58.545541 19.829941 23.607073 4.800105 53.430676 23.607073 70.349078 6.924742s4.013202-49.889615 5.272246-75.778705c0.865593-18.177446-13.692102-15.738049-25.102187-16.288881a31.476098 31.476098 0 0 0-24.236595-20.459463c-49.889615-10.701873-82.546066-42.335351-96.789-91.595444-3.541061-12.118298-8.734617-21.482437-21.482437-25.023498-3.619751-35.882751-31.476098-20.852915-50.755207-24.315285-36.197512-6.53129-49.102712 5.429627-48.001049 45.08951 3.14761 109.61551 1.101663 219.30971 1.101663 329.00391 0 81.837854-31.003956 111.268005-112.369668 106.940041l-23.607073-19.121729c-0.15738-5.114866-1.259044-10.387112-8.026405-11.174015z"
      fill="#040303"
      p-id="3504"
    ></path>
    <path
      d="M405.806611 786.591534c-27.935037-16.21019-54.296268-34.387637-73.575378-61.142319-2.439398-8.813307-4.564034-17.626615-7.396883-26.282542-41.86321-126.769983 33.207283-240.241315 166.272485-251.415329a12.433059 12.433059 0 0 1 3.777132 2.596778 155.491922 155.491922 0 0 0-1.101663 23.607073c3.06892 30.925266 1.652495 52.407702-41.469759 51.6208-32.971212-0.550832-56.814356 24.944807-65.627663 58.0734-7.869024 29.823602-7.869024 59.017683 22.82017 78.690244-1.416424 6.924741 3.06892 9.52152 8.262476 11.646156l23.607073 19.121729c27.777656 27.620276 60.434107 30.689195 93.720081 14.164244 32.026929-15.738049 46.742005-44.696059 46.742005-80.264049V281.478858c0-43.358324 24.393976-59.175063 69.404795-45.089509 12.74782 3.541061 17.941376 12.9052 21.482436 25.023497 14.242934 49.260093 47.214146 80.893571 96.789 91.595444a31.476098 31.476098 0 0 1 24.236595 20.459463 340.413995 340.413995 0 0 0-1.022973 42.886183c2.911539 28.800629-7.396883 35.725371-35.882751 30.689196-36.118822-6.4526-68.381822-21.954578-104.343263-40.289405 0 72.552405 0.944283 139.045661 0 205.460227a185.158144 185.158144 0 0 1-165.092132 179.413756c-27.935037 3.06892-54.138888-6.29522-81.601783-5.036176z"
      fill="#FBF7F8"
      p-id="3505"
    ></path>
    <path
      d="M405.806611 786.591534c27.305515-1.259044 53.666746 7.869024 81.601783 4.642724a185.158144 185.158144 0 0 0 164.46261-179.885897c1.180354-66.414566 0-132.907822 0-205.460227 35.961441 18.334827 68.224441 33.836805 104.343263 40.289405 28.485868 5.036176 38.79429-1.888566 35.882751-30.689195a340.413995 340.413995 0 0 1 1.022973-42.886183c11.410085 0.865593 25.96778-1.888566 25.102188 16.28888-1.259044 25.88909 11.488776 58.781612-5.272246 75.778705s-46.427244-2.124637-70.349078-6.924741c-21.482437-4.406654-44.774749-32.813832-58.545542-19.829942-12.118298 11.488776-4.957485 43.987846-5.193556 67.201469-0.629522 47.214146 2.045946 94.428293-1.180353 141.642439-4.721415 67.909681-38.79429 119.766551-100.880893 148.331109-56.971737 26.361232-115.674659 30.059673-170.9939-8.498546z"
      fill="#EA2254"
      p-id="3506"
    ></path>
    <path
      d="M650.84803 235.523756c-45.01082-14.085554-69.404795 1.731185-69.404795 45.08951v345.528861c0 35.56799-14.715076 64.36862-46.742005 80.264049-33.285973 16.524951-65.942424 13.456032-93.72008-14.164244 81.365712 4.327963 112.290978-25.102188 112.369668-106.940042 0-109.6942 2.045946-219.3884-1.101663-329.00391-1.101663-39.345122 11.803537-51.6208 48.001049-45.089509 18.885659 3.462371 46.978076-11.567466 50.597826 24.315285z"
      fill="#68BDC3"
      p-id="3507"
    ></path>
    <path
      d="M491.106835 447.751344C358.041633 458.925358 282.97114 572.39669 324.83435 699.166673c2.832849 8.655927 4.957485 17.469234 7.396883 26.282542a172.252944 172.252944 0 0 1-31.476098-192.712408c28.485868-62.952195 98.834946-108.828607 165.249512-108.041705 16.761022 0.236071 30.217054 1.259044 25.102188 23.056242z"
      fill="#69C3C9"
      p-id="3508"
    ></path>
    <path
      d="M409.662433 661.867497c-30.610505-19.43649-30.767885-48.630571-22.820171-78.690243 8.813307-33.128593 32.656451-58.624232 65.627664-58.0734 43.122254 0.786902 44.538678-20.695534 41.469758-51.6208a155.491922 155.491922 0 0 1 1.101664-23.607074c17.705305-2.754159 27.620276 2.675468 26.282541 22.662791a327.351415 327.351415 0 0 0 0 43.043563c1.416424 19.751251-1.809876 28.328488-26.518612 29.666222-59.883276 3.698441-83.411659 38.7156-85.142844 116.618941z"
      fill="#EA2254"
      p-id="3509"
    ></path>
    <path d="M417.531457 673.120202c-5.193556-2.124637-9.6789-4.721415-8.262475-11.646156 7.160812 0.786902 8.262476 6.059149 8.262475 11.646156z" :fill="color" p-id="3510"></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>

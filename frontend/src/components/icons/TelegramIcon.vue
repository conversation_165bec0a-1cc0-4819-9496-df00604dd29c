<template>
  <svg t="1740395779028" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3030" :width="size" :height="size">
    <path
      d="M512 66.4C264.6 66.4 64 265.9 64 512c0 246.1 200.6 445.6 448 445.6S960 758.1 960 512c0-246.1-200.6-445.6-448-445.6z m232.1 243.3l-80.7 420.5c-5.6 29.9-22 37.1-44.5 23.3L496.1 653.1l-59 63.3c-6.8 7.6-12.5 13.8-24.6 13.8l8.3-138.9 227.8-227.2c10.1-10.2-2.1-15.1-15.4-6.2L351.9 554.6l-121.6-42.7c-26.1-8.2-26.4-28.2 5.9-42.7L709.9 267c21.7-10.8 42.4 5.9 34.2 42.7z"
      :fill="color"
      p-id="3031"
    ></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>

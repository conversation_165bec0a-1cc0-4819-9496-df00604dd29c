<template>
  <AccountItem :item="apiVirtualAccount" />
  <AccountItem v-for="item in accountList" :key="item.id" :item="item" />
</template>

<script lang="ts" setup>
import AccountItem from './AccountItem.vue';
import { useAccountStore } from '@/stores/chat/accounts';

const accountStore = useAccountStore();
const { apiVirtualAccount, accountList } = storeToRefs(accountStore);
</script>

<style lang="scss" scoped></style>

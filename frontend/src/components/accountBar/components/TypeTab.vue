<template>
  <div class="type-tab">
    <span v-for="item in tabList" :key="item.value" class="cursor-pointer" :class="{ active: item.value === modelValue }" @click="handleTabClick(item.value)">
      {{ item.label }}
    </span>
  </div>
</template>

<script lang="ts" setup>
interface TabItem {
  label: string;
  value: string | number;
}

interface Props {
  modelValue: string | number;
}

interface Emits {
  (e: 'update:modelValue', value: string | number): void;
  (e: 'change', value: string | number): void;
}
const tabList = ref<TabItem[]>([
  {
    label: '全部',
    value: 'all'
  },
  {
    label: '官方',
    value: 'official'
  },
  {
    label: '扫码',
    value: 'scan'
  },
  {
    label: '协议',
    value: 'protocol'
  }
]);
defineProps<Props>();
const emit = defineEmits<Emits>();

const handleTabClick = (value: string | number) => {
  emit('update:modelValue', value);
  emit('change', value);
};
</script>

<style lang="scss" scoped>
.type-tab {
  @apply flex items-center justify-between bg-#F2F3F5 p-3px text-xs;
  span {
    @apply flex-1 text-center py-1px;
    &.active {
      @apply bg-white;
      color: var(--el-color-primary);
    }
  }
}
</style>

<template>
    <el-select class="ugly-select" popper-class="ugly-select-popper" v-model="modelValue" v-bind="$attrs" :loading="loading" clearable @visible-change="handleVisibleChange" @clear="handleClear">
      <template #label="{ label, value }">
        <span>{{ label }}</span>
        <span v-if="findSelectedGroup(value)?.groupTotal"
          >: <i class="theme-color">{{ findSelectedGroup(value)?.onlineCount }}</i> / {{ findSelectedGroup(value)?.groupTotal }}</span
        >
      </template>
      <el-option v-for="item in options" :key="item[selectProps.value]" :label="getItems(item, '粉丝')" :value="item[selectProps.value]">
        <div>
          <i>{{ item[selectProps.label] }}</i>
          <span v-if="item.storedData" class="ml-2 text-xs text-gray">
            <i>(入库数量：</i><i class="theme-color ml-2">{{ item.storedData }}</i> ，剩余数量{{ item.remainingData }})
          </span>
        </div>
      </el-option>
      <template #empty>
        <div v-if="loading" class="flex-center py-2">
          <el-icon class="is-loading mr-2">
            <Loading />
          </el-icon>
          加载中...
        </div>
        <div v-else class="py-2 text-center text-gray-400">暂无数据</div>
      </template>
  
      <template #loading>
        <el-icon v-if="loading" class="el-input__icon">
          <Loading />
        </el-icon>
      </template>
  
      <template #footer>
        <div class="flex-center border-t px-2 py-1">
          <el-pagination v-model:current-page="page" :page-size="pageSize" :total="total" size="small" background :pager-count="5" layout="prev, pager, next" @current-change="handleCurrentChange" />
        </div>
      </template>
    </el-select>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue';
  import { Loading } from '@element-plus/icons-vue';
  import type { SelectPageProps } from '@/components/SelectPage/types';
  
  const props = withDefaults(defineProps<SelectPageProps>(), {
    modelValue: undefined,
    props: () => ({
      label: 'label',
      value: 'value'
    }),
    requestApi: () => Promise.resolve({ list: [], total: 0 }),
    immediate: true
  });
  
  const emit = defineEmits(['update:modelValue', 'change']);
  
  // 计算属性
  const selectProps = computed(() => props.props);
  const modelValue = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  });
  
  // 响应式状态
  const loading = ref(false);
  const options = ref<any[]>([]);
  const page = ref(1);
  const pageSize = ref(20);
  const total = ref(0);
  const isDataLoaded = ref(false);
  
  const getItems = (item, val) => {
    let str = '';
    if (val === '粉丝') {
      str = `${item.dataName}(入库数量：${item.storedData}，剩余数量${item.remainingData})`;
    }
    return str;
  };
  
  // 获取数据
  const fetchData = async () => {
    if (loading.value) return;
  
    loading.value = true;
    try {
      const { list, total: totalCount } = await props.requestApi({
        page: page.value,
        pageSize: pageSize.value
      });
      options.value = list || [];
      total.value = totalCount || 0;
    } catch (error) {
      console.error('获取数据失败:', error);
      options.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
      isDataLoaded.value = true;
    }
  };
  
  // 事件处理
  const handleCurrentChange = (val: number) => {
    page.value = val;
    fetchData();
  };
  
  const handleVisibleChange = (visible: boolean) => {
    if (visible && !isDataLoaded.value) {
      fetchData();
    }
  };
  
  const handleClear = () => {
    emit('change', undefined);
  };
  
  // 计算属性
  const findSelectedGroup = computed(() => {
    return (value: any) => options.value.find((item) => item.groupId === value);
  });
  
  // 监听器父组件传值
  watch(modelValue, (val) => {
    emit('change', val);
  });
  
  // 监听初始值
  watch(
    () => props.modelValue,
    (val) => {
      if (val && !isDataLoaded.value) {
        fetchData();
      }
    },
    { immediate: true }
  );
  
  onMounted(() => {
    if (props.immediate || props.modelValue) {
      fetchData();
    }
  });
  
  // 对外暴露的方法
  defineExpose({
    refresh: () => {
      isDataLoaded.value = false;
      page.value = 1;
      fetchData();
    }
  });
  </script>
  
  <style lang="scss" scoped>
  :deep(.el-select-dropdown__wrap) {
    max-height: 300px;
  }
  
  :deep(.el-select-dropdown__list) {
    padding: 0;
  }
  
  :deep(.el-pagination) {
    justify-content: center;
    --el-pagination-button-width: 24px;
    --el-pagination-button-height: 24px;
  }
  </style>
  
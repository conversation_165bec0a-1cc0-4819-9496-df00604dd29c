<template>
  <div class="contact-list-container">
    <div class="flex-between px-4 mt-4">
      <strong class="text-22px font-bold">对话</strong>
      <div class="flex-center gap-2">
        <el-button circle plain class="a2c-circle-btn" @click="handleAddContact">
          <i class="iconfont icon-tianjiarenyuan text-2xl"></i>
        </el-button>
        <el-button circle plain class="a2c-circle-btn">
          <i class="iconfont icon-xuanxiang-chuizhi text-2xl"></i>
        </el-button>
      </div>
    </div>
    <!-- 搜索 -->
    <SearchFrom v-model="search" />
    <!-- 联系人状态tab -->
    <TypeTab v-model="type" />
    <!-- 联系人列表 -->
    <div class="contact-list overflow-y-auto h-full mt-2">
      <ContactItem v-for="item in contactList" :key="item.friendAccount" :item="item" />
    </div>
    <UploadContact ref="uploadContactRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SearchFrom from './SearchFrom.vue';
import TypeTab from './TypeTab.vue';
import ContactItem from './ContactItem.vue';
import UploadContact from './UploadContact.vue';

const search = ref('');
const type = ref(null);

// 联系人列表
const contactList = ref([
  {
    'friendAccount': '***********',
    'unReadNum': 0,
    'headImg': null,
    'messageVO': {
      'message': 'I have a good thing to sharegood thing to sharegood thing to share with you\uD83E\uDD8B',
      'filePath': '',
      'seconds': null,
      'coverUrl': null,
      'type': 1,
      'messageId': 'C670F6724CBD19AA0B93B5944C21A9E6',
      'fileName': null,
      'id': '1940952394465669121',
      'senderAccount': '***************',
      'receiverAccount': null,
      'messageStatus': 2,
      'translation': 'I have a goodgood thing to sharegood thing to share thing to share with you\uD83E\uDD8B',
      'targetLanguage': 'en',
      'isRead': 1,
      'receiverRead': 0,
      'phoneState': 0,
      'sharePhone': null,
      'text': null,
      'url': null,
      'businessMobile': null,
      'title': null,
      'body': null,
      'template': null,
      'messageSource': 4,
      'createdAt': '2025-07-04 09:54:47'
    },
    'nickname': null,
    'isDuplicateFans': null
  },
  {
    'friendAccount': '***********',
    'unReadNum': 10,
    'headImg': null,
    'messageVO': {
      'message': 'I have a good thing to share with you\uD83E\uDD8B',
      'filePath': '',
      'seconds': null,
      'coverUrl': null,
      'type': 1,
      'messageId': 'E452C01E0810F53190D0BE5195851828',
      'fileName': null,
      'id': '1940952104828006402',
      'senderAccount': '***************',
      'receiverAccount': null,
      'messageStatus': 2,
      'translation': 'I have a goodgood thing to sharegood thing to sharegood thing to share thing to share with you\uD83E\uDD8B',
      'targetLanguage': 'en',
      'isRead': 1,
      'receiverRead': 0,
      'phoneState': 0,
      'sharePhone': null,
      'text': null,
      'url': null,
      'businessMobile': null,
      'title': null,
      'body': null,
      'template': null,
      'messageSource': 4,
      'createdAt': '2025-07-04 09:53:38'
    },
    'nickname': null,
    'isDuplicateFans': null
  },
  {
    'friendAccount': '*************',
    'unReadNum': 0,
    'headImg': null,
    'messageVO': null,
    'nickname': null,
    'isDuplicateFans': null
  },
  {
    'friendAccount': '***********',
    'unReadNum': 0,
    'headImg': 'https://file.a2c.chat/2025/07/18/cbbc0a6c76204bfbbe645858ac59bac7.png',
    'messageVO': null,
    'nickname': null,
    'isDuplicateFans': null
  }
]);

const uploadContactRef = ref();

const handleAddContact = () => {
  uploadContactRef.value.open();
};


</script>

<style lang="scss" scoped>
.contact-list-container {
  width: 422px;
  height: 100vh;
  background-color: #fff;
  border-right: 1px solid #e5e5e5;
  transform: translate(0px, 0px);
  position: relative;
}
</style>

<template>
  <div class="contact-info-bar border-r-1">
    <ul class="w-full flex flex-col items-center gap-3">
        <li v-for="(item, index) in tabItems" :key="index" :class="{ 'active': active === index }" @click="active = index">
            <i :class="`iconfont ${active === index ? item.hoverIcon : item.icon}`"></i>
        </li>
    </ul>
    <el-avatar class="scale-85 cursor-pointer" src="https://cube.elemecdn.com/0/88/03b0d39583f4b3a790e941881d41jpeg.jpeg" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const active = ref(0);
const tabItems = ref([
    {
        label: '对话',
        icon: 'icon-duihua',
        hoverIcon: 'icon-a-duihuatianchong',
    },
    {
        label: '群组',
        icon: 'icon-yonghu-qunzu-L',
        hoverIcon: 'icon-yonghu-qunzu-F',
    },
]);

</script>

<style lang="scss" scoped>
.contact-info-bar {
  @apply w-65px h-full flex flex-col items-center justify-between bg-#F0F2F5 pt-10px pb-3;
  
  li {
    @apply w-10 h-10 flex items-center justify-center cursor-pointer rounded-full text-2xl text-#4c5a64 relative;
    &:hover, &.active{
        @apply bg-#D9DBDE;
    }
  }
}
</style>
<template>
  <div class="flex items-center mt-10px search-input px-4">
    <el-input v-model="search" :placeholder="`请输入${searchTypeList.find(item => item.value === searchType)?.label}`">
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
      <template #suffix>
        <!-- 下拉 -->
        <el-divider direction="vertical" />
        <el-dropdown>
          <el-button link>
            <span>{{ searchTypeList.find(item => item.value === searchType)?.label }}</span>
            <el-icon class="text-xl ml-2">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in searchTypeList" :key="item.value" @click="searchType = item.value">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-input>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Search, ArrowDown } from '@element-plus/icons-vue';

const search = ref('');
const searchType = ref('phone');
const searchTypeList = ref([
  {
    label: '手机号',
    value: 'phone',
  },
  {
    label: '备注',
    value: 'remark',
  },
  {
    label: '昵称',
    value: 'nickname',
  },
]);
</script>

<style lang="scss" scoped>
.search-input {
  :deep(.el-input__wrapper) {
    background-color: #f0f2f5;
    border: 0;
    box-shadow: none;
    padding-top: 2px;
    padding-bottom: 2px;
  }
}
</style>

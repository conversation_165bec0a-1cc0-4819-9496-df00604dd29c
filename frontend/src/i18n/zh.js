export default {
  common: {
    add: '添加',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    search: '搜索',
    loading: '加载中...',
    success: '成功',
    failed: '失败',
    error: '错误',
    warning: '警告',
    tip: '提示',
    yes: '是',
    no: '否',
    operation: '操作',
    systemTip: '系统提示',
    copy: '复制',
    chinese: '简体中文',
    english: 'English',
    khmer: 'ភាសាខ្មែរ'
  },
  window: {
    minimize: '最小化',
    maximize: '最大化',
    restore: '还原',
    close: '关闭'
  },
  menu: {
    home: '首页',
    subtitle: 'A2C',
    quickReply: '快速回复',
    moreSetting: '更多设置',
    translateConfig: '翻译配置',
    customWeb: '自定义网页'
  },
  home: {
    time: '当前时间',
    logout: '退出登录',
    logoutConfirm: '确定要退出登录吗？',
    copySuccess: '复制成功！',
    copyFailed: '复制失败！',
    accountInfo: '账户信息',
    availableChars: '可用字符数',
    expirationTime: '到期时间',
    remainingDays: '剩余 {days} 天',
    deviceId: '设备标识',
    coreFeatures: '核心功能',
    features: {
      multiAccount: {
        title: '多账号同时登录',
        desc: '支持同一平台多个账号同时在线管理'
      },
      messageManagement: {
        title: '统一消息管理',
        desc: '集中处理所有平台的聊天和通知'
      },
      translation: {
        title: '高效沟通实时翻译',
        desc: '双向翻译支持 10+ 种语言'
      },
      localTranslation: {
        title: '本地翻译',
        desc: '支持本地API翻译功能，保护数据隐私'
      }
    },
    supportAndHelp: '支持与帮助',
    officialChannel: 'A2C 官方频道',
    channelDesc: '加入我们的 Telegram 频道，获取最新公告和使用技巧',
    tags: {
      updates: '产品更新',
      support: '技术支持',
      feedback: '问题反馈'
    },
    joinChannel: '加入频道'
  },
  login: {
    title: '欢迎使用',
    loginKey: '请输入座席登录密钥',
    loginButton: '登录',
    forgotText: '忘记席位密钥 请登录客服管理查看，',
    gotoLink: '立即前往',
    seatSelect: '请选择席位类型',
    keyHint: '我之前办理了6个，理论上席位密钥数量',
    rightTitle1: '用A2C',
    rightTitle2: '在WhatsApp上拓展业务所需的一切',
    features: {
      feature1: '进粉统计，粉丝备注/完成精准粉丝画像',
      feature2: '对用户进行标签化管理，精准推送营销内容，构建私域流量',
      feature3: '群发消息、快捷回复，各类信息一键发送',
      feature4: '支持全球 200 多种语言，多条翻译线路，提供实时翻译服务'
    },
    partners: '受到全球多个国家地区及多家优秀企业客户的信赖',
    agreement: {
      text: '登录即已阅读并同意',
      userAgreement: '《用户协议》',
      and: '和',
      privacyPolicy: '《隐私政策》'
    },
    autoLogin: '自动登录',
    validation: {
      required: '请输入座席登录密钥',
      length: '密钥长度应在5到20个字符之间',
      agreement: '请阅读并同意用户协议和隐私政策'
    },
    lineSelector: {
      placeholder: '切换线路',
      defaultLine: '默认线路',
      customLinePrompt: '输入自定义线路(支持http/https)',
      customLineTitle: '自定义线路',
      inputPlaceholder: '请输入',
      inputErrorMessage: '请输入正确的线路地址',
      lineExists: '该线路已存在',
      addSuccess: '添加线路成功',
      addFailed: '添加线路失败',
      refreshSuccess: '刷新成功',
      refreshFailed: '刷新失败'
    },
    logging: '正在登录...',
    success: '登录成功',
    errors: {
      emptyAuthCode: '请输入授权密钥',
      invalidParams: '参数错误，请检查输入',
      accountNotExist: '账户不存在，请检查授权密钥',
      accountDisabled: '账户已被禁用，请联系管理员',
      expired: '设备授权已过期，请重新获取授权',
      deviceDisabled: '设备已被禁用，请联系管理员',
      networkError: '网络连接失败，请检查网络设置',
      unknown: '登录失败，请稍后重试',
      authFailed: '授权验证失败'
    }
  },
  session: {
    newChat: '新建会话',
    loginAll: '一键登录',
    nickname: '昵称',
    url: '链接',
    proxyConfig: '代理配置',
    proxyStatus: '代理状态',
    proxyType: '代理类型',
    proxyIp: '代理IP',
    proxyPort: '代理端口',
    username: '用户名',
    password: '密码',
    sessionList: '会话列表',
    startAll: '一键启动',
    closeAll: '一键关闭',
    batchDelete: '批量删除',
    confirmDelete: '确认删除所选会话？',
    confirmDeleteSingle: '确认删除该会话吗？',
    remarks: '备注',
    searchPlaceholder: '备注、用户名',
    createTime: '创建于',
    sessionRecord: '会话记录',
    show: '显示',
    start: '启动',
    close: '关闭',
    starting: '会话启动中 请稍等！',
    closeSuccess: '会话关闭成功',
    urlRequired: '网址不能为空',
    proxyEnabled: '开启验证',
    proxyDisabled: '关闭验证',
    delete: '删除',
    proxySettings: '代理设置',
    proxyConfigSaveSuccess: '代理配置保存成功',
    enableProxy: '启用代理服务器',
    proxyProtocol: '代理协议',
    selectProxyProtocol: '选择代理协议',
    hostAddress: '主机地址',
    enterHostAddress: '请输入主机地址',
    portNumber: '端口号',
    enterPortNumber: '请输入主机端口号',
    enableProxyAuth: '启用代理服务器验证',
    enterUsername: '请输入用户名',
    enterPassword: '请输入密码',
    restartRequired: '重启会话后生效'
  },
  translate: {
    google: '谷歌翻译',
    baidu: '百度翻译',
    youdao: '有道翻译',
    huoshan: '火山翻译',
    xiaoniu: '小牛翻译',
    apiKey: 'API密钥',
    secretKey: '密钥',
    appId: '应用ID',
    settings: '翻译设置',
    clearCacheTitle: '确认清理当前会话历史翻译缓存吗？',
    mode: '翻译模式',
    localTranslate: '本地翻译',
    cloudTranslate: '云端翻译',
    tooltipContent: '本地翻译不消耗字符数\n使用前请先配置翻译API',
    route: '翻译线路',
    selectRoute: '请选择翻译线路',
    realTimeReceive: '接收消息实时翻译',
    realTimeSend: '发送消息实时翻译',
    sourceLanguage: '源语言',
    targetLanguage: '目标语言',
    autoDetect: '自动检测',
    friendIndependent: '好友独立发送翻译开关',
    preview: '翻译预览'
  },
  quickReply: {
    title: '快捷回复',
    tooltipContent: '单击到输入框进行翻译\n双击按钮发送原文',
    searchPlaceholder: '请输入标题或者关键内容过滤',
    noData: '暂无数据',
    send: '发送'
  },
  userInfo: {
    title: '联系人信息',
    phoneNumber: '手机号',
    nickname: '昵称',
    country: '国家',
    gender: '性别',
    male: '男',
    female: '女',
    salesInfo: '销售信息',
    tradeActivity: '交易活动',
    customerLevel: '客户等级',
    remarks: '备注',
    enterRemarks: '请输入',
    followUpRecords: '跟进记录',
    selectPlaceholder: '请选择',
    activityStatus: {
      negotiating: '沟通中',
      scheduled: '已预约',
      ordered: '已下单',
      paid: '已付款',
      shipped: '已发货'
    },
    customerLevels: {
      normal: '普通',
      medium: '一般',
      important: '重要',
      critical: '核心'
    }
  },
  rightMenu: {
    refreshTip: '即将刷新当前页面',
    refresh: '刷新',
    close: '关闭',
    devTools: '开发者工具'
  },
  quickReplyConfig: {
    group: {
      title: '快捷分组',
      addGroup: '添加分组',
      editGroup: '修改分组',
      groupName: '分组名称',
      enterGroupName: '请输入分组名称',
      deleteConfirm: '确认删除该分组吗？',
      filterPlaceholder: '请输入分组名称过滤'
    },
    reply: {
      title: '快捷回复',
      addReply: '新增快捷回复',
      editReply: '修改快捷回复',
      clearReply: '清空回复',
      clearConfirm: '是否确认清空所有回复?',
      deleteConfirm: '确认删除该快捷回复？',
      selectGroup: '请先选择分组',
      remark: '备注',
      enterRemark: '请输入备注',
      type: '类型',
      content: '内容',
      enterContent: '请输入内容',
      text: '文字',
      image: '图片',
      video: '视频',
      resource: '资源'
    },
    message: {
      addSuccess: '添加成功',
      editSuccess: '修改成功',
      deleteSuccess: '删除成功',
      clearSuccess: '清空成功',
      operationFailed: '操作失败'
    }
  }
};

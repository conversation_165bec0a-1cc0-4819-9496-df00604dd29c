export const importTips = `
  <p>1.账号需要入库成功才能登录，登录占用端口。</p>
  <p>2.端口不充足时，入库成功的账号不能登录。</p>
  <p>3.设置账号可用状态控制账号是否能登录。</p>
  <p>4.封号的账号在账号列表/回收站中保留10天后自动删除。</p>
  <p>5.账号被其他用户入库过的会导致入库失败。</p>
`;
export const batchOnlineTips = `
  <p>1.选择登录环境和IP时，应尽量与小号注册时的环境和国家保持一致，以降低小号被封的风险，并增加小号权重。</p>
  <p>2.静态IP保持固定不变，特别适合需要稳定连接的操作场景。我们建议您在接粉阶段使用<span class="text-red-500">静态IP</span>。静态IP能够确保账号连接的稳定性，避免因IP变更导致的异常登录问题，从而提高粉丝接收的效率，并确保账号安全。</p>
`;
export const transferAccountTips = `
  <p>1.账号转移是将A2C主管账号下的子账号转移至其他主管账号下。</p>
  <p>2.转移前需要将账号下线，在线账号不支持转移。</p>
`;
export const wsTransferAccountTips = `
  <p>1.账号转移属于全局操作</p>
  <p>2.选中WS账号列表分组，开始任务后将分组的账号移入【目标账号】中的【目标账号分组】</p>
`;
export const wsModifyTips = `
  <p>1.本系统仅提供修改资料功能，不能突破WS官方风控限制，请根据需求合理使用!</p>
  <p>2.如在修改资料过程中造成账号异常时，所造成的后果由自己承担!</p>
  <p>3.账号首次登录后，建议不直接修改资料，可以先养号后再操作适量的账号执行修改资料！</p>
`;
export const accountInheritanceTips = `
  <div class="pb-2">
    <span class="text-gray-600">1. <span class="text-gray-400">将被继承账号的聊天记录转移到另一个账号</span></span>
    <span class="text-gray-600">2. <span class="text-gray-400">执行账号继承功能后，被继承账号移出当前席位，继承账号自动移入被继承账号所在席位</span></span>
  </div>
  <div class="pb-2">
    <span class="text-gray-600"
      >3. <span class="text-gray-400">只有<span class="text-red-400">已分配席位</span>的主号方可被继承。（在账号列表页批量选中账号后点击账号继承按钮，会过滤掉不满足条件的账号）</span></span
    >
    <span class="text-gray-600">4. <span class="text-gray-400">继承的账号需满足在线且当前未分配至席位内时</span></span>
  </div>
  <div>
    <span class="text-gray-600">5. <span class="text-gray-400">账号继承任务只会一对一执行，只需选好被继承账号与继承账号即会进行一对一分配</span></span>
    <span class="text-gray-600">6. <span class="text-gray-400">账号在账号继承任务进行中时，请勿执行其他操作</span></span>
  </div>
`;
// 聊天室-账号继承风险提示
export const chatInheritanceTips = `
  <p>1.所输入的账号必须是主管下的账号。</p>
  <p>2.所输入的账号必须是在线且没有绑定座席的账号。</p>
  <p>3.确认换绑后将继承原账号所有信息，如通讯录好友、聊天记录等。</p>
`;

// 系统智能标签说明
export const systemSmartTagTips = `
  系统智能标签不可修改，会根据设置的规则自动变化<br>
  休息期:手动设置账号进入休息期，系统会根据设置的时间<br>自动移除，也可主动移除；休息期间不参与群发养号任务<br>
  群发标签：自动记录账号群发或打粉的数量<br>
  养号标签：自动记录账号养号的次数
`;

// 回收站功能说明
export const recycleBinTips = `
  <p>1、被移入回收站的账号可保留10天，超过10天自动清除。</p>
  <p>2、处于回收站的账号可重新还原至账号分组中。</p>
`;
// 粉丝计数器名词定义
export const fansCounterTips = `
  <p class="mb-3 text-red-500">以下进粉数据均为粉丝主动发送首条消息才纳入统计，上传通讯录、主动发送首条消息不纳入统计</p>
  <p class="mb-3 font-bold">一、新粉总数：</p>
  <p class="mb-3 text-gray-500">1.统计移入该工单后所有席位进粉总数（工单重粉不计入，移入工单前已添加粉丝不计入）</p>
  <p class="mb-3 text-gray-500">2.席位如果从工单移出，将停止该席位在本工单内的进粉统计</p>
  <p class="mb-3 text-gray-500">3.重置工单后新粉总数清零</p>
  <p class="mb-3 font-bold">二、当日新粉数：</p>
  <p class="mb-3 text-gray-500">1.统计移入该工单后所有席位在上一次置零时间至当前时间段的进粉数量，如果置零时间后执行过
重置工单，则以重置工单时间为准(席位重粉不计入、移入工单前已添加粉丝不计入)</p>
  <p class="mb-3 text-gray-500">2.席位如果从工单移出，将停止该席位在本工单内的进粉统计</p>
  <p class="mb-3 text-gray-500">3.重置工单后当日进粉数清零</p>
  <p class="mb-3 text-gray-500">4.每日到达【当日置零时间】时重置清零，重新统计</p>
  <p class="mb-3 font-bold">三、历史进粉总数：</p>
  <p class="mb-3 text-gray-500">1.统计工单历史所有移入过的席位进粉数据(包含重粉数)，不受工单重置、每日置零时间影响</p>
  <p class="mb-3 font-bold">四、历史新粉总数：</p>
  <p class="mb-3 text-gray-500">1.统计工单历史所有移入过的席位新粉数据，不受工单重置、每日置零时间影响</p>
  <p class="mb-3 font-bold">五、历史重粉总数：</p>
  <p class="mb-3 text-gray-500">1.统计工单历史所有移入过的席位重粉总数</p>
  <p class="mb-3 font-bold">六、当日重粉总数：</p>
  <p class="mb-3 text-gray-500">1.统计移入该工单后所有席位在上一次置零时间至当前时间段的重粉数量，如果置零时间后执行过重置工单，则以重置工单时间为准</p>
  <p class="mb-3 font-bold">七、当日置零时间：</p>
  <p class="mb-3 text-gray-500">1、当日置零时间影响【当日新粉数】、【当日重粉总数】统计数量</p>
  <p class="mb-3 font-bold">八、重置工单时间：</p>
  <p class="mb-3 text-gray-500">1、计数器工单重置后，除历史相关进粉数据，其他所有统计数据清零并且重新开始计算</p>
`;

// 回复规则
export const replyRulesTips = `
  <p>1、优先精准匹配。</p>
  <p>2、同个匹配模式下存在多个相同关键词，随机触发回复。</p>
  <p>3、精准匹配模式：精准匹配关键词，回复对应消息内容。</p>
  <p>4、模糊匹配模式：①模糊匹配关键词，回复对应消息内容。②粉丝回复内容涉及到多个关键词时，优先触发长度最长的关键词。如长度相同，则随机触发。</p>
`;

// 敏感词功能说明
export const sensitiveWordTips = `
  <p>1、当粉丝发出的消息中含有设置好的【敏感词】，则将该粉丝移入黑名单。
  <p>2、当粉丝处于黑名单中时，聊天室将不接收该粉丝任何消息。</p>
  <p>3、粉丝处于白名单时，该粉丝触发【敏感词】也不会将该粉丝移入黑名单中，且正常接收所有消息。</p>
  <p>4、敏感词功能为全局设置（设置成功后对所有粉丝生效）。</p>
`;

// 养号功能说明
export const maintenanceTips = `
  <p>1.点击新建WS养号任务即可创建新的养号任务</p>
  <p>2.【选择发起聊天的账号】和【选择被添加的账号】选择的是都账号列表分组中的在线账号</p>
  <p>3.开始任务后在【选择发起聊天的账号】和【选择被添加的账号】选中的账号会按照【养号设置】里的参数配置互相发送随机消息进行互聊养号</p>
  <p>4.新用户建议使用辅助模式，规则模板中选择系统默认模板，以免带来不必要损失</p>
`;
// 群发功能说明
export const massPostTips = `
  <p class="text-red-500">1.建议账号提前养号，再用适量的账号执行群发！！！</p>
  <p>2.若需将群发消息转发至聊天室，请提前将账号分配至客服座席。</p>
  <p>3.回复规则：设置多句话术时，首句将主动推送，而后续话术则采用触发式回复，即需粉丝回复后才推送下一句。</p>
  <p>4.任务状态说明：在执行中、停止群发或已完成状态下，触发式回复功能正常运作，而关闭任务状态下将不会推送后续的话术，可根据封号情况调整应对策略。</p>
  <p>5.注意事项：针对同一批账号，若在其停止任务或已完成后重新创建群发任务，则会导致该账号上一条任务的触发式回复功能失效。</p>
  <p>6.本系统仅提供群发功能，群发效果受帐号权重、IP、话术、发送频率、地区风控政策及投诉举报等多种外部因素影响，且无法规避WS官方风控限制，请根据需求合理使用。</p>
`;

// 群发列表页风险提示
export const massPostListRiskTips = `
  <p>1.点击新建WS群发创建新的群发任务</p>
  <p>2.【选择WS账号】选择的是账号列表分组中的在线账号。当任务开始后会使用分组内的账号发送消息</p>
  <p>3.【选择WS数据】可以选择WS粉丝数据、群发未回复的数据、本地上传作为群发对象</p>
  <p>4.下载话术模板输入话术内容，然后上传模板到系统里，当任务开始后就会根据设置好的话术进行群发</p>
`;

// 群发新增页风险提示
export const massPostRiskTips = `
  <p>1.建议账号提前养号，再用适量的账号执行群发！！！</p>
  <p class="text-gray-600">2.本系统仅提供群发功能，不能突破WS官方风控限制，请根据需求合理使用！</p>
  <p class="text-gray-600">3.群发效果涉及帐号权重，ip，话术内容，频繁程度，国家地区风控政策，投诉举报等因素，这些非A2C系统能控制！</p>
  <p class="text-gray-600">4.如在群发过程中造成账号异常时,所造成的后果由自己承担！</p>
`;

// IP管理功能说明
export const ipManagementTips = `
  <p>1.对IP需求更高的用户可以使用个人IP进行账号登录</p>
  <p>2.个人IP需要自行购买，再批量导入进系统进行操作</p>
  <p>3.个人IP与账号是绑定关系，每次下线不会解绑，只有进行IP释放以后才会与账号解绑</p>
  <p>4.如果IP类型出现“未知IP类型”状态，需要在【批量操作】中手动更改IP类型。</p>
`;

// 炒群功能介绍
export const activeGroupTips = `
  <p>1.点击新建WS炒群任务创建新的炒群任务</p>
  <p>2.在【选择剧本】中选择已上传至系统的剧本或下载模板输入炒群话术</p>
  <p>3.每输入一条群链接，则选择的账号都会对这些群执行炒群操作</p>
  <p>4.开始任务后会根据话术内设置的分组和输入的内容进行炒群话术的推送</p>
`;
// 炒群风险提醒
export const activeGroupRiskTips = `
  <p>1.建议账号提前养号，再用适量的账号执行炒群！！！</p>
  <p class="text-gray-600">2.本系统仅提供炒群功能，不能突破WS官方风控限制，请根据需求合理使用！</p>
  <p class="text-gray-600">3.如在炒群过程中造成账号异常时,所造成的后果由自己承担！</p>
`;

// 批量进群风险提醒
export const batchEntryRiskTips = `
  <p>1.建议账号提前养号，再用适量的账号执行进群！！！</p>
  <p class="text-gray-600">2.本系统仅提供进群功能，不能突破WS官方风控限制，请根据需求合理使用！</p>
  <p class="text-gray-600">3.如在进群过程中造成账号异常时，所造成的后果由自己承担！</p>
`;

// 群聊群发风险提示
export const groupChatRiskTips = `
  <p>1.建议账号提前养号，再用适量的账号执行群聊群发！！！</p>
  <p class="text-gray-600">2.本功能仅提供群聊的群发功能，不能突破WS官方风控限制，请根据需求合理使用！群聊群发群发效果涉及帐号权重，ip，话术内容，频繁程度，国家地区风控政策，投诉举报等因素，这些非A2C系统能控制！</p>
  <p class="text-gray-600">3.如在群发过程中造成账号异常时,所造成的后果由自己承担！</p>
`;

// 群成员获取风险提醒
export const getMemberRiskTips = `
  <p>1.建议账号提前养号，再用适量的账号执行群成员获取！！！</p>
  <p class="text-gray-600">2.本系统仅提供进群获取成员功能，不能突破WS官方风控限制，请根据需求合理使用!</p>
  <p class="text-gray-600">3.如在任务过程中造成账号异常时，所造成的后果由自己承担!</p>
`;

// 群聊剧本使用说明
export const groupChatScriptTips = `
  <h3 class="text-base font-bold mt-0 pt-0 mb-2">剧本使用说明</h3>
  <p class="mb-2">1.剧本的分组需要创建后，【使用剧本】才可以选择需要的剧本组</p>
  <p class="mb-2">2.剧本组中的内容可编辑删除，话术数量代表账号发送消息的数量</p>
  <p class="mb-2">3.剧本的分组以及分组中的内容在编辑删除时，会影响任务的执行，建议在无任务执行状况下操作</p>
`;

// 个人IP登录说明
export const personalIpTips = `
  <p class="font-bold mb-1">个人IP登录说明</p>
  <p>1.当选择个人IP登录时，若账号存在登录记录，系统将自动按照上次的记录进行登录操作，而本次所选择的IP将不会被采用。</p>
  <p>2.若账号已有登录记录，但需要更换登录IP，则必须先手动释放当前的IP，之后新的IP选择方可效。</p>
`;

//账号集成说明
export const accountIntegrationTips = `
  <p>同步第三方数据时，WABA账号、API账号、模板及key将同步更新，若API账号或模板无法使用，请完成同步后重试。</p>
`;

// 模板说明
export const templateTips = `
  <p>提示：文件类模板申请通过后，原文件超过30天群发将无法正常显示，届时请编辑模板重新提交或群发消息更换变量。</p>
`;

// 消息群发说明
export const broadcastTips = `
  <p>1.不同地区的群发消息单价也不同，请在群发计划查看【价格计算器】。</p>
  <p>2.群发计划所扣除的费用，会在账户明细中生成记录。</p>
  <p>3.群发消息失败，扣除的费用会在48小时后退还至账户。</p>
`;

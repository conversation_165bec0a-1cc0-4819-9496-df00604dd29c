export const commonChat = {
  // 素材文件类型 1文本 2图片 3语音 4视频 5文件
  resourceType: {
    1: '文本',
    2: '图片',
    3: '语音',
    4: '视频',
    5: '文件'
  },
  // 消息类型 1文本 2图片 3视频 4音频 5文件 6语音通话 7视频通话 8名片 9超链接 10广告 11图文链接 12模板消息 13图文模板
  msgType: {
    1: '文本',
    2: '图片',
    3: '视频',
    4: '音频',
    5: '文件',
    6: '语音通话',
    7: '视频通话',
    8: '名片',
    9: '超链接',
    10: '广告',
    11: '图文链接',
    12: '模板消息',
    13: '图文模板'
  },
  // 消息状态
  msgState: {
    0: '未发送',
    1: '发送中',
    2: '发送成功',
    3: '发送失败'
  },
  // 通话状态
  phoneState: {
    '-1': '呼叫中',
    0: '未接听',
    1: '已接听',
    2: '已拒绝',
    3: '已取消'
  },
  // 翻译线路中的中国大陆代码
  chinaCode: ['zh_CN', 'ZH', 'zh-CHS']
};

// 工单批量操作类型
export const batchOperationType = {
  1: '批量删除工单',
  2: '批量重置工单',
  3: '批量设置置零时间',
  4: '批量修改去重维度',
  5: '移动至其他分组'
};

// 工单类型
export const counterType = {
  1: '席位计数工单',
  2: '账号计数工单'
};

// 去重维度
export const repeatType = {
  1: '粉丝库',
  2: '根据席位去重规则',
  3: '当前工单'
};

// 去重失效时长
export const durationType = {
  0: '全量粉丝',
  30: '30天前',
  90: '90天前',
  180: '180天前',
  365: '1年前',
  1: '自定义'
};

// 账号在线状态
export const onlineStatus = {
  0: '掉线',
  1: '在线',
  2: '登录中',
  3: '登录失败',
  4: '离线',
  5: '已失效',
  6: '停用'
};

// IP管理批量操作类型
export const ipBatchOperationType = {
  setAllocation: '分配设置',
  ipMoveGroup: '移动分组',
  netCheck: '网络检测',
  ipStartAllocation: 'IP启动分配',
  ipDisableAllocation: 'IP禁用分配',
  ipDelete: '批量删除',
  ipExport: '批量导出',
  ipTypeChange: '修改IP类型',
  ipChangeCountry: '修改国家',
  ipRelease: '批量释放IP'
  // ipFreeze: '冻结IP'
  // notes: '编辑备注'
};

// 剧本发言类型
export const scriptChatType = {
  1: '文本',
  2: '语音',
  3: '图片',
  4: '视频',
  // 5: '超链接',
  6: '自定义上传名片',
  // 7: '名片推送链接',
  8: '计数器工单名片'
};

// 任务状态
export const taskState = {
  0: '待开始',
  1: '进行中',
  2: '已完成',
  3: '已关闭'
};

// 发送状态
export const sendStatus = {
  0: '发送中',
  1: '发送成功',
  2: '发送失败'
};

// 模板标题上传提示
export const fileTips = {
  2: {
    label: '选择图片',
    type: ['.jpeg', '.png', '.jpg'],
    size: '5',
    tip: '横向 3:2 方块 1:1 纵向 2:3'
  },
  3: {
    label: '选择视频',
    type: ['.mp4', '.3gp'],
    size: '16',
    tip: ''
  },
  4: {
    label: '点击上传',
    type: ['.pdf'],
    size: '100',
    tip: ''
  }
};

// 消息来源 0-默认消息、1-关键词回复、2-好友自动回复、3-快捷回复、4-营销任务、5-API模板
export const messageSource = {
  0: '默认消息',
  1: '关键词回复',
  2: '好友自动回复',
  3: '快捷回复',
  4: '营销任务',
  5: 'API模板'
};

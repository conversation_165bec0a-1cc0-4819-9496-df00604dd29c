import { ChatType } from '@/enum/ChatType';

export interface Account {
  id: string | number;
  //用户账号
  account: string;
  //账号类型 1 协议号  2 官方号
  accountType: 1 | 2;
  //账号在线状态 (0掉线 1在线 2登录中 3登录失败 4离线 5已失效 6已停用)
  onlineStatus: number;
  //头像
  headImg?: string;
  //登录端点（1安卓端 2web端扫码）
  loginEndpoint?: number;
  //用户昵称
  nickname?: string;
  //原因
  reason?: string;
  //备注
  remark?: string;
  //置顶状态(0未置顶 1已置顶)
  topStatus?: number;
  //可用状态 (1可登录 2不可登录)
  useStatus?: number;

  // other
  totalUnReadNum?: number;
  chatType?: ChatType;
  ip?: string;
  
  // 前端专用
  isRefreshing?: boolean
}

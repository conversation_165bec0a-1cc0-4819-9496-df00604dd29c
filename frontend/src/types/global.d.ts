// 全局类型声明

declare global {
  interface Window {
    electronAPI?: any
  }
}

// 组件类型
export interface ComponentsMap {
  [key: string]: any
}

// API 线路类型
export interface ApiLine {
  name: string
  url: string
  ms?: number
}

// 菜单项类型
export interface MenuItem {
  label: string
  value: string
  icon?: string
}

// 通用响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export {}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 1rem;
  line-height: 1.5;
  color: #21262c;
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#app {
  height: 100%;
}
li{
  list-style: none;
}
.theme-color {
  color: #10b981;
}
a {
  text-decoration: none;
  color: #10b981;
}

i {
  font-style: normal;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: rgba(0, 0, 0, 0.24);
  }

  &:active {
    background: rgba(0, 0, 0, 0.36);
  }
}

// 隐藏滚动条角落
::-webkit-scrollbar-corner {
  background: transparent;
}

// 自定义样式
.border-r-1{
  border-right: 1px solid #d1d7db;
}
.flex-center{
  @apply flex items-center justify-center;
}
.flex-between{
  @apply flex items-center justify-between;
}

.ghost-button {
  @apply outline-none !p-0 !border-none;
}
.part-drawer {
  .el-drawer {
      box-shadow: none !important;
  }
}
.a2c-circle-btn.el-button.is-circle {
  border: none;
  width: 40px;
  height: 40px;
  &:hover {
    background-color: rgba(217, 219, 222, 0.3);
    color: #4c5a64;
  }
  &+.a2c-circle-btn {
    margin-left: 5px;
  }
}
.a2c-dropdown {
  .el-popper__arrow {
    display: none;
    opacity: 0;
  }
  &.el-popper{
    border-radius: 6px;
    min-width: 100px;
    max-width: 200px;
    padding-top: 10px;
    .el-dropdown-menu{
      padding: 10px;
      border-radius: 6px;
    }
    .el-dropdown-menu__item{
      padding: 10px;
      border-radius: 6px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}

.icon-avatar,
.icon-group-avatar {
  display: inline-block;
  vertical-align: middle;
  width: 49px;
  height: 49px;
  background-image: url(@/assets/icons/png/avatar.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

// select 样式
.ugly-select {
  .el-select__wrapper {
    box-shadow: none !important;
    padding: 0 0 10px;
    border-radius: 0;
    border-bottom: 2px solid #667781;
    font-size: 18px;
    color: #3B4A54;
    .el-select__caret {
      color: #8696A0;
      zoom: 1.3;
    }
  }
}
.ugly-select-popper {
  li{
    padding-top: 5px;
    padding-bottom: 5px;
    height: auto;
  }
}
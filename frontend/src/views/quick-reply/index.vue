<template>
  <div class="quick-reply">
    <!--新建分组弹出层-->
    <el-dialog v-model="groupVisible" :title="groupTitle" width="400">
      <div class="add-group-dialog-form">
        <el-form :model="groupForm" :rules="rules" ref="groupFormRef" label-width="80px" @submit.prevent>
          <el-form-item :label="t('quickReplyConfig.group.groupName')" prop="name">
            <el-input v-model="groupForm.name" :placeholder="t('quickReplyConfig.group.enterGroupName')" @keyup.enter.prevent="handleSaveGroup"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="add-group-dialog-footer">
          <el-button @click="groupVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSaveGroup">{{ t('common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!--新建快捷回复弹出层-->
    <el-dialog v-model="replyVisible" :title="replyTitle" width="600">
      <div class="add-group-dialog-form">
        <el-form :model="replyForm" :rules="rules" ref="replyFormRef" label-width="80px" @submit.prevent>
          <el-form-item :label="t('quickReplyConfig.reply.remark')" prop="remark">
            <el-input v-model="replyForm.remark" :placeholder="t('quickReplyConfig.reply.enterRemark')" @keyup.enter.prevent="handleSaveReply"></el-input>
          </el-form-item>
          <el-form-item :label="t('quickReplyConfig.reply.type')" prop="type">
            <el-radio-group v-model="replyForm.type">
              <el-radio value="text">{{ t('quickReplyConfig.reply.text') }}</el-radio>
              <el-radio disabled value="image">{{ t('quickReplyConfig.reply.image') }}</el-radio>
              <el-radio disabled value="video">{{ t('quickReplyConfig.reply.video') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('quickReplyConfig.reply.content')" prop="content">
            <el-input :autosize="{ minRows: 4, maxRows: 8 }" v-model="replyForm.content" type="textarea" :placeholder="t('quickReplyConfig.reply.enterContent')" @keyup.enter.prevent></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="add-group-dialog-footer">
          <el-button @click="replyVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSaveReply">{{ t('common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 左侧操作栏 -->
    <div class="quick-reply-left">
      <div class="reply-item-title">
        <div class="left-header">
          <div class="header-title">
            <el-text tag="b" size="large">{{ t('quickReplyConfig.group.title') }}</el-text>
          </div>
          <div class="header-icon">
            <el-icon @click="handleAddGroup(null)"><Plus /></el-icon>
          </div>
        </div>
        <div class="header-search">
          <el-input :placeholder="t('quickReplyConfig.group.enterGroupName')" @input="handleSearch" v-model="groupName" :suffix-icon="Search"></el-input>
        </div>
      </div>

      <div class="left-content">
        <div @click="handleChangeGroup(item)" class="content-item" v-for="item in groups" :class="{ 'item-selected': currentGroupId === item.id }">
          <div class="item-left">
            <el-text truncated>{{ item.name }}</el-text>
            <el-text>({{ item.contentCount }})</el-text>
          </div>
          <div class="item-right">
            <div class="edit-icon">
              <el-icon @click.stop="handleAddGroup(item)"><Edit /></el-icon>
            </div>
            <div class="delete-icon" @click.stop="() => {}">
              <el-popconfirm width="180" :confirm-button-text="t('common.yes')" :cancel-button-text="t('common.no')" @confirm="handleDeleteGroup(item)" :title="t('quickReplyConfig.group.deleteConfirm')">
                <template #reference>
                  <el-icon><Delete /></el-icon>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </div>
        <el-empty :image-size="100" v-if="groups.length <= 0" :description="t('quickReply.noData')" />
      </div>
    </div>
    <!-- 右侧内容部分 -->
    <div class="quick-reply-right">
      <div class="right-header">
        <el-button size="small" plain type="primary" @click="handleAddReply(null)">{{ t('quickReplyConfig.reply.addReply') }}</el-button>
        <el-button size="small" plain type="danger" :disabled="!(tableData.length > 0)" @click="handleClearReply">{{ t('quickReplyConfig.reply.clearReply') }}</el-button>
      </div>
      <div class="right-content">
        <el-table height="100%" :data="tableData" border>
          <el-table-column align="center" prop="id" width="80" label="ID"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" prop="remark" :label="t('quickReplyConfig.reply.remark')"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" prop="content" :label="t('quickReplyConfig.reply.content')"></el-table-column>
          <el-table-column align="center" prop="url" :label="t('quickReplyConfig.reply.resource')" min-width="160"> - </el-table-column>
          <el-table-column align="center" :label="t('common.operation')" width="150" fixed="right">
            <template #default="{ row }">
              <el-button @click="handleAddReply(row)" size="small" round type="primary">{{ t('common.edit') }}</el-button>
              <el-popconfirm placement="left" :confirm-button-text="t('common.yes')" :cancel-button-text="t('common.no')" @confirm="handleDeleteReply(row)" :title="t('quickReplyConfig.reply.deleteConfirm')">
                <template #reference>
                  <el-button round size="small" type="danger">{{ t('common.delete') }}</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Delete, Edit, Plus, Search } from '@element-plus/icons-vue';
import { nextTick, onMounted, ref, watch } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { ElButton, ElMessage, ElMessageBox, ElTable, ElTableColumn } from 'element-plus';
import { searchCollection } from '@/utils/common';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const groupName = ref('');

const handleSearch = async () => {
  if (groupName.value === '') {
    await initData();
  }
  groups.value = searchCollection(groups.value, groupName.value, true);
};

const currentGroupId = ref(0);
const groups = ref([]);
const tableData = ref([]);
const initData = async () => {
  const res = await ipc.invoke(ipcApiRoute.getGroups, {});
  if (res.status) {
    groups.value = res.data;
  }
  await getTableData();
};
const getTableData = async () => {
  // 查找对应的分组对象
  const row = groups.value.find((item) => item.id === currentGroupId.value);
  if (row) {
    tableData.value = row.contents || [];
  }
};
const groupVisible = ref(false);
const replyVisible = ref(false);
// 表单校验规则
const rules = ref({
  name: [{ required: true, message: t('quickReplyConfig.group.enterGroupName'), trigger: 'blur' }],
  type: [{ required: true, message: t('quickReplyConfig.reply.type'), trigger: 'blur' }],
  remark: [{ required: true, message: t('quickReplyConfig.reply.enterRemark'), trigger: 'blur' }],
  content: [{ required: true, message: t('quickReplyConfig.reply.enterContent'), trigger: 'blur' }]
});
const groupFormRef = ref(null);
const replyFormRef = ref(null);
const groupForm = ref({
  name: ''
});
const replyForm = ref({
  id: '',
  type: '',
  remark: '',
  content: ''
});
const groupTitle = ref('');
const replyTitle = ref('');
const handleAddGroup = async (group) => {
  await nextTick(() => {
    groupFormRef.value?.clearValidate();
  });
  if (group) {
    groupTitle.value = t('quickReplyConfig.group.editGroup');
    groupForm.value.id = group.id;
    groupForm.value.name = group.name;
  } else {
    groupTitle.value = t('quickReplyConfig.group.addGroup');
    groupForm.value = {};
  }
  groupVisible.value = true;
};
const handleDeleteGroup = async (group) => {
  const res = await ipc.invoke(ipcApiRoute.deleteGroup, { id: group.id });
  if (res.status) {
    ElMessage({
      message: t('quickReplyConfig.message.deleteSuccess'),
      type: 'success',
      offset: 40
    });
    if (group.id === currentGroupId.value) {
      currentGroupId.value = 0;
      tableData.value = [];
    }
    await initData();
  } else {
    ElMessage({
      message: t('quickReplyConfig.message.operationFailed'),
      type: 'error',
      offset: 40
    });
  }
};
const handleSaveGroup = async () => {
  groupFormRef.value.validate(async (valid) => {
    if (valid) {
      const id = groupForm.value.id;
      if (id) {
        const res = await ipc.invoke(ipcApiRoute.editGroup, { name: groupForm.value.name, id: id });
        if (res.status) {
          ElMessage({
            message: t('quickReplyConfig.message.editSuccess'),
            type: 'success',
            offset: 40
          });
          groupVisible.value = false;
          await initData();
        } else {
          ElMessage({
            message: t('quickReplyConfig.message.operationFailed'),
            type: 'error',
            offset: 40
          });
        }
      } else {
        const res = await ipc.invoke(ipcApiRoute.addGroup, { name: groupForm.value.name });
        if (res.status) {
          ElMessage({
            message: t('quickReplyConfig.message.addSuccess'),
            type: 'success',
            offset: 40
          });
          groupVisible.value = false;
          await initData();
        } else {
          ElMessage({
            message: t('quickReplyConfig.message.operationFailed'),
            type: 'error',
            offset: 40
          });
        }
      }
    }
  });
};
const handleSaveReply = async () => {
  replyFormRef.value.validate(async (valid) => {
    if (valid) {
      //判断是新增还是编辑
      const id = replyForm.value.id;
      if (id) {
        const args = {
          id: id,
          remark: replyForm.value.remark,
          content: replyForm.value.content,
          type: replyForm.value.type,
          url: '',
          groupId: currentGroupId.value
        };
        const res = await ipc.invoke(ipcApiRoute.editReply, args);
        if (res.status) {
          ElMessage({
            message: t('quickReplyConfig.message.editSuccess'),
            type: 'success',
            offset: 40
          });
          replyVisible.value = false;
          await initData();
        } else {
          ElMessage({
            message: t('quickReplyConfig.message.operationFailed'),
            type: 'error',
            offset: 40
          });
        }
      } else {
        const args = {
          remark: replyForm.value.remark,
          content: replyForm.value.content,
          type: replyForm.value.type,
          url: '',
          groupId: currentGroupId.value
        };
        const res = await ipc.invoke(ipcApiRoute.addReply, args);
        if (res.status) {
          ElMessage({
            message: t('quickReplyConfig.message.addSuccess'),
            type: 'success',
            offset: 40
          });
          replyVisible.value = false;
          await initData();
        } else {
          ElMessage({
            message: t('quickReplyConfig.message.operationFailed'),
            type: 'error',
            offset: 40
          });
        }
      }
    }
  });
};
const handleAddReply = async (record) => {
  await nextTick(() => {
    replyFormRef.value?.clearValidate();
  });
  if (currentGroupId.value === 0) {
    ElMessage({
      message: t('quickReplyConfig.reply.selectGroup'),
      type: 'error',
      offset: 40
    });
    return;
  }
  replyVisible.value = true;
  if (record) {
    replyTitle.value = t('quickReplyConfig.reply.editReply');
    replyForm.value.id = record.id;
    replyForm.value.remark = record.remark;
    replyForm.value.type = record.type;
    replyForm.value.groupId = record.groupId;
    replyForm.value.content = record.content;
  } else {
    replyForm.value = {};
    replyForm.value.type = 'text';
    replyTitle.value = t('quickReplyConfig.reply.addReply');
  }
};
const handleClearReply = async () => {
  ElMessageBox.confirm(t('quickReplyConfig.reply.clearConfirm'), t('common.tip'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  })
    .then(async () => {
      const groupId = currentGroupId.value;
      const res = await ipc.invoke(ipcApiRoute.deleteAllReply, { groupId: groupId });
      if (res.status) {
        ElMessage({
          message: t('quickReplyConfig.message.clearSuccess'),
          type: 'success',
          offset: 40
        });
        await initData();
      } else {
        ElMessage({
          message: t('quickReplyConfig.message.operationFailed'),
          type: 'error',
          offset: 40
        });
      }
    })
    .catch(() => {});
};
const handleDeleteReply = async (row) => {
  const id = row.id;
  const res = await ipc.invoke(ipcApiRoute.deleteReply, { id: id });
  if (res.status) {
    ElMessage({
      message: t('quickReplyConfig.message.deleteSuccess'),
      type: 'success',
      offset: 40
    });
    await initData();
  } else {
    ElMessage({
      message: t('quickReplyConfig.message.operationFailed'),
      type: 'error',
      offset: 40
    });
  }
};
onMounted(async () => {
  await initData();
  if (currentGroupId.value === 0 && groups.value.length > 0) {
    currentGroupId.value = groups.value[0].id;
  }
});
watch(
  () => currentGroupId.value,
  async (newValue) => {
    // 查找对应的分组对象
    await getTableData();
  },
  { immediate: true }
);

const editItem = async (row) => {};
const deleteItem = async (row) => {};
const handleChangeGroup = async (item) => {
  currentGroupId.value = item.id;
};
</script>

<style scoped lang="scss">
// 定义颜色变量
@selected-color: #f0f0f0;
@border-color: #e1e1e1;
@bg-color: #F0F2F5FF;
.quick-reply {
  height: 100%;
  background-color: var(--el-bg-color);
  display: flex;
  //弹出层部分
  .add-group-dialog-form {
    :deep(.el-input__wrapper) {
      border-radius: 0;
      background-color: var(--el-bg-color-overlay);
      border-color: var(--el-border-color);
    }
    :deep(.el-textarea__inner) {
      border-radius: 0;
      background-color: var(--el-bg-color-overlay);
      color: var(--el-text-color-primary);
    }
  }
  .add-group-dialog-footer {
    :deep(.el-button) {
      border-radius: 0;
      border-color: var(--el-color-primary);
    }
  }
  .quick-reply-left {
    display: flex;
    flex-direction: column; /* 修改为列布局 */
    min-width: 240px;
    width: 240px;
    :deep(.el-text) {
      --el-text-color: var(--el-text-color-primary);
    }
    :deep(.el-input__wrapper) {
      border-radius: 0;
    }
    .reply-item-title {
      display: flex;
      flex-direction: column; /* 修改为列布局 */
      padding: 20px 20px 0 20px;
      .left-header {
        height: 30px;
        width: 100%;
        display: flex;
        user-select: none;
        .header-title {
          display: flex;
          flex: 1;
        }
        .header-icon {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: end;
          cursor: pointer;
        }
      }
      .header-search {
        width: 100%; /* 确保宽度占满父容器 */
        margin-top: 15px; /* 添加一些间距 */
      }
    }

    .left-content {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      margin-top: 10px;
      background-color: var(--el-bg-color);
      .content-item {
        width: 100%;
        height: 45px;
        display: flex;
        cursor: pointer;
        border-bottom: 1px solid var(--el-border-color);
        .item-left {
          display: flex;
          flex: 4;
          max-width: 140px;
          justify-content: start;
          align-items: center;
          gap: 5px;
          user-select: none;
          margin-left: 20px;
        }
        .item-right {
          display: flex;
          flex: 1;
          justify-content: end;
          align-items: center;
          gap: 5px;
          .edit-icon {
            display: flex;
          }
          .delete-icon {
            display: flex;
            margin-right: 20px;
          }
        }
      }
      .item-selected {
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
  .quick-reply-right {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: calc(100% - 240px);
    background-color: var(--el-bg-color-page);
    .right-header {
      padding-left: 10px;
      height: 45px;
      display: flex;
      align-items: center;
    }
    .right-content {
      display: flex;
      overflow: auto;
      flex: 1;
      padding-left: 10px;
    }
  }
}
</style>

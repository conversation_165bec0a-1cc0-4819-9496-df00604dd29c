<template>
  <div class="w-500px h-full">
    <div class="font-size-18px font-bold">翻译设置</div>
    <div class="txt">以下设置将作为新创建会话的默认翻译设置</div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">接收自动翻译设置</div>
      <div>
        <el-switch v-model="receiveAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="translateRoute" placeholder="请选择翻译路线">
          <el-option
              v-for="route in trRoutes"
              :key="route.id"
              :label="route.chineseName"
              :value="route.id">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="translateLanguage" placeholder="请选择翻译语言">
          <el-option
              v-for="language in trLanguages"
              :key="language.languageCode"
              :label="language.chineseName"
              :value="language.languageCode">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">群组自动翻译</div>
      <div>
        <el-switch v-model="groupAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">发送自动翻译设置</div>
      <div>
        <el-switch v-model="sendAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="sendTranslateRoute" placeholder="请选择翻译路线">
          <el-option
              v-for="route in trRoutes"
              :key="route.id"
              :label="route.chineseName"
              :value="route.id">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="sendTranslateLanguage" placeholder="请选择翻译语言">
          <el-option
              v-for="language in trLanguages"
              :key="language.languageCode"
              :label="language.chineseName"
              :value="language.languageCode">
          </el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {trLanguageList, trRouteList} from '@/api/setting/index.ts';
import type {TranslateLanguage, TranslateRoute} from '@/api/setting/types.ts';

const receiveAutoTranslate = ref(false);
const groupAutoTranslate = ref(false);
const sendAutoTranslate = ref(false);
const translateRoute = ref('');
const translateLanguage = ref('');
const sendTranslateRoute = ref('');
const sendTranslateLanguage = ref('');
const trRoutes = ref<TranslateRoute[]>([]);
const trLanguages = ref<TranslateLanguage[]>([]);

const getTranslateRoutes = async () => {
  try {
    const result = await trRouteList() as any;
    console.log(result)
    if (result?.data) {
      trRoutes.value = result.data;
    }
  } catch (error) {
    console.error('获取翻译路线失败:', error);
  }
};

const getTranslateLanguages = async () => {
  try {
    const result = await trLanguageList({type: 8}) as any;
    console.log(result)
    if (result?.data) {
      trLanguages.value = result.data;
    }
  } catch (error) {
    console.error('获取翻译语言失败:', error);
  }
};

onMounted(() => {
  getTranslateRoutes();
  getTranslateLanguages();
});

</script>
<style lang="scss" scoped>
.txt {
  @apply text-#8F959E font-size-14px mt-10px;
}
</style>

<template>
  <div class="w-full h-full flex flex-col">
    <!--    头部区域-->
    <div class="header w-full p-l4.5 h-12.5 bg-white flex items-center shadow-sm">设置</div>
    <!-- 主体区域-->
    <main class="w-full flex-1 bg-white overflow-hidden">
      <div class="flex w-full h-full">
        <div class="w-61 h-full pt bg-#e8e9eb px-3.5">
          <div v-for="(item, index) in menuList" :key="index" :class="{ active: currentView === item.value }" class="w-full h-11.5 flex items-center justify-start pl-3.5 cursor-pointer mb" @click="changeView(item.value)">
            <i :class="item.icon" class="iconfont mr-2"></i>
            <span>{{ item.name }}</span>
          </div>
        </div>
        <el-scrollbar class="w-full h-full">
          <div class="h-full flex-1 pl10 pt8 overflow-auto">
            <component :is="currentViewList[currentView]"></component>
          </div>
        </el-scrollbar>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import transition from './components/translate.vue';
import agent from './components/agent.vue';
import system from './components/system.vue';
import about from './components/about.vue';

const route = useRoute();
const router = useRouter();

const currentViewList = {
  transition,
  agent,
  system,
  about
};

// 根据路由参数初始化当前视图
const currentView = ref((route.query.tab as string) || 'transition');

const menuList = [
  {
    name: '翻译设置',
    value: 'transition',
    icon: 'icon-fanyi'
  },
  {
    name: '全局代理',
    value: 'agent',
    icon: 'icon-fuwuqi'
  },
  {
    name: '系统设置',
    value: 'system',
    icon: 'icon-shezhi'
  },
  {
    name: '关于',
    value: 'about',
    icon: 'icon-guanyuwomen'
  }
];

const changeView = (view: string) => {
  currentView.value = view;
  // 更新路由参数，但不触发页面刷新
  router.replace({
    name: 'settings',
    query: { tab: view }
  });
};

// 监听路由变化，同步更新当前视图
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      currentView.value = newTab;
    }
  }
);
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #dcdfe6;
}

.active {
  color: #10b981;
  background: #dedfe0;
  border-radius: 6px;
}
</style>

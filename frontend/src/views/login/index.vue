<template>
  <div class="login-container">
    <!-- 使用 TitleBar 组件 -->
    <TitleBar :dark-buttons="true" :show-center="false" :show-left="true" :transparent="true"/>

    <!-- 登录内容区域 -->
    <div class="login-content drag-area">
      <div class="w-full flex justify-center items-center">
        <!-- 左侧登录表单 -->
        <div class="login-form-section w-1/2 mr10">
          <el-card class="login-card" shadow="hover">
            <!-- 语言选择器 -->
            <div class="language-selector">
              <el-dropdown placement="bottom-end" @command="handleLanguageChange">
                <el-button circle class="language-button" text>
                  <span class="text-lg">🌐</span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="zh">
                      <span class="text-base mr-2">🇨🇳</span>
                      {{ t('common.chinese') }}
                    </el-dropdown-item>
                    <el-dropdown-item command="en">
                      <span class="text-base mr-2">🇺🇸</span>
                      {{ t('common.english') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <!-- 登录标题 -->
            <div class="login-header">
              <h1 class="login-title">
                {{ t('login.title') }}
                <img alt="logo" class="logo-image" src="@/assets/images/login/img.png"/>
              </h1>
              <p class="login-subtitle">
                {{ t('login.forgotText') }}
                <a class="link" href="https://user.a2c.chat/" target="_blank">{{ t('login.gotoLink') }}</a>
              </p>
            </div>
            <!-- 登录表单 -->
            <el-form ref="loginForm" :model="loginFormModel" :rules="rules" class="login-form" @submit="handleLogin">
              <!-- 登录密钥输入框 -->
              <el-form-item class="form-item" prop="seatLoginKey">
                <el-input v-model="loginFormModel.seatLoginKey" :placeholder="t('login.loginKey')" clearable
                          size="large"/>
              </el-form-item>

              <!-- 线路选择下拉框 -->
              <el-form-item class="form-item">
                <check-line/>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item class="form-item" prop="agreement">
                <el-checkbox v-model="loginFormModel.agreement" class="agreement-checkbox">
                  {{ t('login.agreement.text') }}
                  <a class="link" href="https://www.a2c.chat/rules/service-agreement.html"
                     target="_blank">{{ t('login.agreement.userAgreement') }}</a>
                  {{ t('login.agreement.and') }}
                  <a class="link" href="https://www.a2c.chat/rules/privacy-policy.html"
                     target="_blank">{{ t('login.agreement.privacyPolicy') }}</a>
                </el-checkbox>
              </el-form-item>

              <!-- 自动登录 -->
              <el-form-item class="form-item" prop="automaticLogon">
                <el-checkbox v-model="loginFormModel.autoLogin" class="auto-login-checkbox">
                  {{ t('login.autoLogin') }}
                </el-checkbox>
              </el-form-item>

              <!-- 登录按钮 -->
              <el-form-item class="form-item">
                <div class="login-btn w-full flex justify-between items-center" size="large" type="primary"
                     @click="handleLogin">
                  <span>{{ t('login.loginButton') }}</span>
                  <span class="arrow-icon">→</span>
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 右侧功能介绍 -->
        <div class="feature-section w-1/2 ml10 drag-area">
          <div class="feature-content">
            <div class="feature-title">
              <span class="color-#10B981">{{ t('login.rightTitle1') }} </span>
              <span>
                {{ t('login.rightTitle2') }}
              </span>
            </div>

            <!-- 功能特点列表 -->
            <div class="features-list">
              <div v-for="i in 4" :key="i" class="feature-item">
                <div class="check-icon">
                  <svg fill="none" height="16" viewBox="0 0 16 16" width="16">
                    <path d="M13.5 4.5L6 12L2.5 8.5" stroke="#10B981" stroke-linecap="round" stroke-linejoin="round"
                          stroke-width="2"/>
                  </svg>
                </div>
                <span class="feature-text">
                  {{ t(`login.features.feature${i}`) }}
                </span>
              </div>
            </div>

            <!-- 合作伙伴介绍 -->
            <div class="partners-section">
              <p class="partners-text">{{ t('login.partners') }}</p>
              <div class="partners-logos">
                <img alt="partner-logo" class="partner-logo" src="@/assets/images/login/d1.png"/>
                <img alt="partner-logo" class="partner-logo" src="@/assets/images/login/d2.png"/>
                <img alt="partner-logo" class="partner-logo" src="@/assets/images/login/d3.png"/>
                <img alt="partner-logo" class="partner-logo" src="@/assets/images/login/d4.png"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type {FormInstance} from 'element-plus';
import {useI18n} from 'vue-i18n';
import TitleBar from '@/layout/components/TitleBar.vue';
import CheckLine from '@/components/checkLine/index.vue';
import {useRouter} from 'vue-router';
import { useUserInfoStore } from '@/stores/modules/userInfo';
import type { SimpleLoginParams } from '@/api/common/types';

const router = useRouter();
const userInfoStore = useUserInfoStore();

const {t, locale} = useI18n();

// 语言切换
const handleLanguageChange = (key: string) => {
  locale.value = key;
};

const loginForm = ref<FormInstance | null>(null);

// 简化的登录表单数据
const loginFormModel = reactive<SimpleLoginParams & { agreement: boolean }>({
  seatLoginKey: 'F0sW1tP9pLPwM9Xvt8',
  autoLogin: true,
  agreement: true
});

// 动态规则，支持国际化
const rules = computed(() => ({
  seatLoginKey: [
    {
      required: true,
      message: t('login.validation.required'),
      trigger: 'blur'
    },
    {min: 5, max: 20, message: t('login.validation.length'), trigger: 'blur'}
  ],
  agreement: [
    {
      validator: (rule: any, value: boolean, callback: any) => {
        if (!value) {
          callback(new Error(t('login.validation.agreement')));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
}));

// 登录处理
const handleLogin = async () => {
  // 先进行表单验证
  if (!loginForm.value) return;

  try {
    await loginForm.value.validate();

    // 只传递必要的登录参数给store
    const loginParams: SimpleLoginParams = {
      seatLoginKey: loginFormModel.seatLoginKey,
      autoLogin: loginFormModel.autoLogin
    };

    // 调用store中的登录方法
    const result = await userInfoStore.login(loginParams);
    
    if (result.success) {
      // 登录成功，跳转到主页
      router.push('/dashboard');
    } else {
      // 显示错误信息
      console.error('登录失败:', result.message);
      // 这里可以添加错误提示的UI显示
    }
  } catch (error) {
    // 捕获表单验证错误
    console.error('表单验证失败:', error);
  }
};
</script>

<style lang="scss" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #e2f8f0 50%, #dff7ed 100%);
  overflow: hidden;
}

.login-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 0;
}

.login-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  width: 100%;
  align-items: center;
}

.login-form-section {
  display: flex;
  justify-content: flex-end;

  .login-card {
    width: 100%;
    max-width: 420px;
    padding: 2rem;
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08),
    0 4px 10px rgba(0, 0, 0, 0.03);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);

    :deep(.el-card__body) {
      padding: 0 !important;
    }
  }
}

.language-selector {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;

  .language-button {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #10b981;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;

    &:hover {
      background: rgba(16, 185, 129, 0.15);
      border-color: rgba(16, 185, 129, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    }
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;

  .login-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    gap: 0.5rem;

    .logo-image {
      height: 2.75rem;
      width: auto;
      object-fit: contain;
    }
  }

  .login-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;

    .link {
      color: #10b981;
      text-decoration: none;
      transition: color 0.2s ease;

      &:hover {
        color: #059669;
        text-decoration: underline;
      }
    }
  }
}

.login-form {
  .form-item {
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .agreement-checkbox,
  .auto-login-checkbox {
    font-size: 0.875rem;
    color: #4b5563;
    line-height: 1.5;

    .link {
      color: #10b981;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        color: #059669;
        text-decoration: underline;
      }
    }
  }

  .login-btn {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    color: #fff;
    padding: 0 1.25rem;

    .arrow-icon {
      font-size: 1.125rem;
      transition: transform 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);

      .arrow-icon {
        transform: translateX(2px);
      }
    }

    &:active {
      transform: translateY(0);
    }
  }

  .key-status-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    line-height: 1.4;

    .status-icon {
      font-size: 1rem;
      flex-shrink: 0;
    }

    .status-text {
      flex: 1;
    }

    &.success {
      background: rgba(16, 185, 129, 0.1);
      color: #059669;
      border: 1px solid rgba(16, 185, 129, 0.2);
    }

    &.warning {
      background: rgba(245, 158, 11, 0.1);
      color: #d97706;
      border: 1px solid rgba(245, 158, 11, 0.2);
    }

    &.info {
      background: rgba(59, 130, 246, 0.1);
      color: #2563eb;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    &.error {
      background: rgba(239, 68, 68, 0.1);
      color: #dc2626;
      border: 1px solid rgba(239, 68, 68, 0.2);
    }
  }
}

.feature-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .feature-content {
    max-width: 800px;
    width: 100%;

    .feature-title {
      font-size: 1.875rem;
      font-weight: 700;
      color: #1f2937;
      line-height: 1.3;
      margin-bottom: 2rem;
    }

    .features-list {
      margin-bottom: 3rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        gap: 0.75rem;

        &:last-child {
          margin-bottom: 0;
        }

        .check-icon {
          flex-shrink: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(16, 185, 129, 0.1);
          border-radius: 50%;
          margin-top: 2px;

          svg {
            width: 12px;
            height: 12px;
          }
        }

        .feature-text {
          font-size: 0.9rem;
          line-height: 1.6;
          color: #4b5563;
          font-weight: 400;
        }
      }
    }

    .partners-section {
      .partners-text {
        color: #28323b;
        margin-bottom: 1.5rem;
      }

      .partners-logos {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: wrap;

        .partner-logo {
          width: 100px !important;
          height: 32px;
          object-fit: contain;
          opacity: 0.7;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}

// Element Plus 组件样式覆盖
:deep(.el-input) {
  .el-input__wrapper {
    border-bottom: 1px solid #e5e7eb;
    background: #ffffff;
    box-shadow: none;
    transition: all 0.3s ease;
    padding: 0 12px;
    height: 48px;
  }

  .el-input__inner {
    font-size: 0.95rem;
    color: #374151;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

:deep(.el-card) {
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

:deep(.el-checkbox) {
  .el-checkbox__label {
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #10b981;
    border-color: #10b981;
  }

  .el-checkbox__inner:hover {
    border-color: #10b981;
  }
}

:deep(.el-button) {
  &.language-button {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #10b981;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(16, 185, 129, 0.15);
      border-color: rgba(16, 185, 129, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    }
  }
}
</style>

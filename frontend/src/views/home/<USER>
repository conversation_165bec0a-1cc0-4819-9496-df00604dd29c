<script setup>
// 1. 图标组件导入优化 - 按功能分组
// 主题相关
// import { <PERSON>, <PERSON> } from '@element-plus/icons-vue'
// 用户信息相关
import { DocumentCopy, Key, Platform, Clock, Coin, SwitchButton } from '@element-plus/icons-vue';
// 功能相关
import { Connection, User, ChatDotRound, Histogram, Setting, Promotion, QuestionFilled, Link } from '@element-plus/icons-vue';

// Element Plus 组件
import { ElMessage, ElMessageBox } from 'element-plus';

// 工具和hooks
import { ipc } from '@/utils/ipcRenderer';
import { useMenuStore } from '@/stores/menuStore';
import { computed, markRaw, onMounted, ref, watch, onUnmounted } from 'vue';
import router from '@/router';
import { ipcApiRoute } from '@/api';
import { useDark } from '@vueuse/core';
import LanguageSwitch from '@/components/global/LanguageSwitch.vue';
import ThemeSwitch from '@/components/global/ThemeSwitch.vue';
import { useI18n } from 'vue-i18n';

// 状态管理
const isDark = useDark();
const menuStore = useMenuStore();
const { t } = useI18n();

// 添加时间相关的状态和方法
const currentTime = ref('');

const updateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 设置定时器并在组件卸载时清除
let timer;
onMounted(() => {
  updateTime();
  timer = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  clearInterval(timer);
});

// 用户认证相关
const checkLogin = async () => {
  const authKey = menuStore.userInfo?.authKey;
  if (authKey) {
    const res = await ipc.invoke(ipcApiRoute.login, { authKey });
    if (res.status) {
      menuStore.setUserInfo(res.data);
    }
  }
};

// 生命周期钩子
onMounted(async () => {
  await checkLogin();
});

// 计算属性
const userInfoCards = computed(() => {
  // 解析到期时间字符串
  const expireTimeStr = menuStore.userInfo.expireTime;
  const expireDate = new Date(expireTimeStr);
  const today = new Date();

  // 计算剩余天数
  const remainingDays = Math.ceil((expireDate - today) / (1000 * 60 * 60 * 24));

  return [
    {
      icon: markRaw(Coin),
      title: t('home.availableChars'),
      value: menuStore.userInfo.totalChars,
      color: 'var(--el-color-purple)',
      bgColor: 'var(--el-color-purple-light-9)'
    },
    {
      icon: markRaw(Clock),
      title: t('home.expirationTime'),
      value: menuStore.userInfo.expireTime,
      subValue: t('home.remainingDays', { days: remainingDays }),
      color: 'var(--el-color-danger)',
      bgColor: 'var(--el-color-danger-light-9)'
    },
    {
      icon: markRaw(Platform),
      title: t('home.deviceId'),
      value: menuStore.userInfo.machineCode,
      color: 'var(--el-color-primary)',
      bgColor: 'var(--el-color-primary-light-9)',
      copyValue: menuStore.userInfo.machineCode,
      showCopy: true
    }
  ];
});

// 功能数据
const mainFeatures = computed(() => [
  {
    icon: markRaw(User),
    title: t('home.features.multiAccount.title'),
    desc: t('home.features.multiAccount.desc'),
    color: '#3498DB'
  },
  {
    icon: markRaw(Connection),
    title: t('home.features.messageManagement.title'),
    desc: t('home.features.messageManagement.desc'),
    color: '#2ECC71'
  },
  {
    icon: markRaw(ChatDotRound),
    title: t('home.features.translation.title'),
    desc: t('home.features.translation.desc'),
    color: '#F39C12'
  },
  {
    icon: markRaw(Histogram),
    title: t('home.features.localTranslation.title'),
    desc: t('home.features.localTranslation.desc'),
    color: '#9B59B6'
  }
]);

// 交互方法
const copyText = (text) => {
  navigator.clipboard
    .writeText(text)
    .then(() =>
      ElMessage({
        message: t('home.copySuccess'),
        type: 'success',
        offset: 40
      })
    )
    .catch(() =>
      ElMessage({
        message: t('home.copyFailed'),
        type: 'error',
        offset: 40
      })
    );
};

const confirmLogout = () => {
  ElMessageBox.confirm(t('home.logoutConfirm'), t('common.tip'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    showClose: false,
    type: 'warning',
    icon: markRaw(QuestionFilled),
    closeOnClickModal: false,
    closeOnPressEscape: false
  }).then(async () => {
    await router.push('/login');
  });
};

const openTelegram = () => {
  ipc.invoke('open-external-link', 'https://t.me/+YNJ9SeOmHJs1ZDY1');
};

const formatAuthKey = (key) => {
  if (!key) return 'ST';
  return `${key.slice(0, 4)}...${key.slice(-2)}`;
};
</script>

<template>
  <div class="modern-home-container" :class="{ 'dark-theme': isDark }">
    <!-- 顶部导航栏 -->
    <div class="app-header">
      <!-- 左侧区域：品牌和时间 -->
      <div class="header-left">
        <!-- 可以考虑添加品牌Logo -->
        <div class="time-display">
          <el-icon><Clock /></el-icon>
          <span>{{ currentTime }}</span>
        </div>
      </div>

      <!-- 右侧区域：功能按钮组 -->
      <div class="header-right">
        <!-- 工具组：语言和主题 -->
        <div class="tool-group">
          <LanguageSwitch :size="32" />
          <ThemeSwitch :size="32" />
        </div>

        <!-- 用户组：用户信息和退出 -->
        <div class="user-group">
          <div class="action-item user-profile" @click="copyText(menuStore.userInfo.authKey)">
            <div class="avatar">{{ menuStore.userInfo.authKey?.substring(0, 2) || 'ST' }}</div>
            <span class="auth-key">{{ formatAuthKey(menuStore.userInfo.authKey) }}</span>
          </div>
          <div class="action-item logout-btn" @click="confirmLogout">
            <el-icon><SwitchButton /></el-icon>
            <span>{{ t('home.logout') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-area">
      <!-- 账户信息部分 -->
      <section class="content-section">
        <div class="modern-header">
          <div class="header-line"></div>
          <h2>{{ t('home.accountInfo') }}</h2>
        </div>

        <!-- 用户信息卡片 -->
        <div class="info-cards">
          <div v-for="(card, index) in userInfoCards" :key="index" class="info-card" :class="{ clickable: card.showCopy }" :style="{ '--card-color': card.color, '--card-bg-color': card.bgColor }" @click="card.showCopy ? copyText(card.copyValue) : null">
            <div class="card-icon">
              <el-icon><component :is="card.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-header">
                <h3 class="card-title">{{ card.title }}</h3>
                <el-button v-if="card.showCopy" type="primary" circle size="small" @click.stop="copyText(card.copyValue)">
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </div>
              <p class="card-value">{{ card.value }}</p>
              <p v-if="card.subValue" class="card-sub-value">{{ card.subValue }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能部分 -->
      <section id="features" class="content-section">
        <div class="modern-header">
          <div class="header-line"></div>
          <h2>{{ t('home.coreFeatures') }}</h2>
        </div>

        <div class="features-grid">
          <div v-for="(feature, index) in mainFeatures" :key="index" class="feature-card" :style="{ '--feature-color': feature.color }">
            <div class="feature-icon">
              <el-icon :size="32"><component :is="feature.icon" /></el-icon>
            </div>
            <div class="feature-content">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.desc }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 修改支持和帮助部分 -->
      <section class="content-section support-section">
        <div class="modern-header">
          <div class="header-line"></div>
          <h2>{{ t('home.supportAndHelp') }}</h2>
        </div>

        <div class="support-card">
          <div class="support-content">
            <div class="support-main">
              <div class="support-icon-wrapper">
                <div class="support-icon">
                  <el-icon :size="32"><Promotion /></el-icon>
                </div>
                <div class="icon-bg"></div>
              </div>

              <div class="support-info">
                <div class="support-text">
                  <h3>{{ t('home.officialChannel') }}</h3>
                  <p>{{ t('home.channelDesc') }}</p>
                </div>
              </div>
            </div>

            <div class="support-footer">
              <div class="support-tags">
                <span>{{ t('home.tags.updates') }}</span>
                <span>{{ t('home.tags.support') }}</span>
                <span>{{ t('home.tags.feedback') }}</span>
              </div>

              <el-button type="primary" @click="openTelegram" class="join-button" :icon="markRaw(Link)">
                {{ t('home.joinChannel') }}
              </el-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modern-home-container {
  height: calc(100vh - 50px); // 设置容器高度
  background: var(--el-bg-color); // 使用 el-plus 背景色
  color: var(--el-text-color-primary);
  width: 100%;
  box-sizing: border-box;

  // 顶部导航栏
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem;
    height: 60px;
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light);
    position: sticky;
    top: 0;
    z-index: 100;

    // 左侧区域：品牌和时间
    .header-left {
      .time-display {
        display: flex;
        align-items: center;
        gap: 0.6rem;
        padding: 0 12px;
        background: var(--el-fill-color-light);
        border-radius: 16px;
        transition: all 0.3s;
        height: 32px;
        cursor: pointer;
        user-select: none;

        .el-icon {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        span {
          font-family: 'Roboto Mono', monospace;
          font-size: 13px;
          color: var(--el-text-color-regular);
          letter-spacing: 0.5px;
          user-select: none;
        }

        &:hover {
          transform: translateY(-1px);
          background: var(--el-fill-color);
        }
      }
    }

    // 右侧区域：功能按钮组
    .header-right {
      display: flex;
      align-items: center;
      gap: 1.2rem; // 增加主要功能组之间的间距

      // 工具组
      .tool-group {
        display: flex;
        align-items: center;
        gap: 0.6rem; // 相关功能之间间距较小
        padding-right: 1.2rem; // 与用户组分隔
        border-right: 1px solid var(--el-border-color-lighter); // 添加分隔线
      }

      // 用户组
      .user-group {
        display: flex;
        align-items: center;
        gap: 0.8rem;
      }

      // 用户信息按钮
      .user-profile {
        gap: 0.6rem;
        background: var(--el-fill-color-light);
        height: 32px;
        padding: 0 12px;
        border-radius: 16px;
        max-width: 140px;
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select: none;

        &:hover {
          transform: translateY(-1px);
          background: var(--el-fill-color);
        }

        .avatar {
          width: 20px; // 调小头像尺寸
          height: 20px;
          border-radius: 4px;
          background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-success));
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px; // 调整字体大小
          font-weight: 600;
          flex-shrink: 0;
          user-select: none;
        }

        .auth-key {
          font-size: 13px;
          color: var(--el-text-color-regular);
          white-space: nowrap;
          user-select: none;
        }
      }

      // 退出登录按钮
      .logout-btn {
        gap: 0.5rem;
        color: var(--el-text-color-regular);
        height: 32px;
        padding: 0 12px;
        border-radius: 16px;
        background: var(--el-fill-color-light);
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select: none;

        &:hover {
          color: var(--el-color-danger);
          transform: translateY(-1px);
          background: var(--el-fill-color);
        }

        .el-icon {
          font-size: 14px; // 调小图标尺寸
        }

        span {
          font-size: 13px;
          user-select: none;
        }
      }
    }
  }

  // 主内容区域
  .content-area {
    margin: 0 auto;
    padding: 1rem 2rem;
    box-sizing: border-box;
    overflow-y: auto;
    height: calc(100vh - 120px);

    // 添加以下样式来隐藏滚动条
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */

    // 标题样式
    .modern-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .header-line {
        width: 4px;
        height: 24px;
        background: var(--el-color-primary);
        border-radius: 2px;
      }

      h2 {
        font-size: 1.4rem;
        font-weight: 600;
        margin: 0;
      }
    }

    // 信息卡片样式
    .info-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1.2rem;
      margin-bottom: 2rem;

      .info-card {
        background: var(--el-bg-color-overlay);
        border-radius: 0.8rem;
        padding: 1.2rem;
        box-shadow: var(--el-box-shadow-light);
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        transition: all 0.3s;
        height: 80%;

        &.clickable {
          cursor: pointer;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--el-box-shadow);
        }

        .card-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background: var(--card-bg-color);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          margin-top: 2px;

          i {
            font-size: 1.2rem;
            color: var(--card-color);
          }
        }

        .card-content {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 24px;

            .card-title {
              font-size: 0.9rem;
              color: var(--el-text-color-secondary);
              margin: 0;
              line-height: 1;
            }
          }

          .card-value {
            color: var(--card-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.4;
            text-align: left;
          }

          .card-sub-value {
            color: var(--card-color);
            font-size: 0.85rem;
            opacity: 0.8;
            margin: 0;
            line-height: 1.2;
            text-align: left;
          }
        }
      }
    }

    // 功能卡片样式
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .feature-card {
        background: var(--el-bg-color-overlay);
        border-radius: 1rem;
        box-shadow: var(--el-box-shadow-light);
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--el-box-shadow);
        }

        .feature-icon {
          height: 80px;
          background: var(--feature-color);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .feature-content {
          padding: 1rem;

          h3 {
            color: var(--feature-color);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
          }

          p {
            color: var(--el-text-color-regular);
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
          }
        }
      }
    }

    // 支持卡片样式
    .support-card {
      background: var(--el-bg-color-overlay);
      border-radius: 1rem;
      padding: 2rem;
      box-shadow: var(--el-box-shadow-light);
      position: relative;
      overflow: hidden;
      border: 1px solid var(--el-border-color-light);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--el-box-shadow);
        border-color: var(--el-color-primary-light-5);

        .support-icon-wrapper {
          .icon-bg {
            transform: scale(1.1);
          }
        }
      }

      .support-content {
        .support-main {
          display: flex;
          align-items: flex-start;
          gap: 2rem;
          margin-bottom: 1.5rem;

          .support-icon-wrapper {
            position: relative;
            width: 64px;
            height: 64px;

            .support-icon {
              width: 64px;
              height: 64px;
              border-radius: 16px;
              background: var(--el-color-primary);
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              z-index: 2;
              box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
            }

            .icon-bg {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 100%;
              height: 100%;
              background: var(--el-color-primary);
              opacity: 0.2;
              border-radius: 16px;
              z-index: 1;
              transition: transform 0.3s;
            }
          }

          .support-info {
            flex: 1;
            min-width: 0;

            .support-text {
              h3 {
                font-size: 1.3rem;
                font-weight: 600;
                margin: 0 0 0.8rem;
                color: var(--el-text-color-primary);
              }

              p {
                color: var(--el-text-color-regular);
                margin: 0;
                font-size: 1rem;
                line-height: 1.5;
              }
            }
          }
        }

        .support-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 1rem;
          border-top: 1px solid var(--el-border-color-lighter);

          .support-tags {
            display: flex;
            gap: 0.8rem;
            flex-wrap: wrap;

            span {
              color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
              padding: 0.4rem 1rem;
              border-radius: 20px;
              font-size: 0.9rem;
              transition: all 0.3s;
              border: 1px solid var(--el-color-primary-light-5);

              &:hover {
                background: var(--el-color-primary-light-8);
                transform: translateY(-1px);
              }
            }
          }

          .join-button {
            height: 40px;
            padding: 0 24px;
            font-size: 1rem;
            border-radius: 20px;
            font-weight: 500;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="proxy-config">
    <!--    顶部标题-->
    <div class="header-container">
      <div class="header-title">
        <el-text tag="b" size="large">{{ t('session.proxyConfig') }}</el-text>
      </div>
    </div>
    <!--    代理开关-->
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('session.enableProxy') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" size="default" v-model="proxyInfo.proxyStatus" />
      </div>
    </div>
    <!--    代理协议类型-->
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('session.proxyProtocol') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="proxyInfo.proxyType" :placeholder="t('session.selectProxyProtocol')" size="default" style="width: 100%">
          <el-option label="HTTP" value="http" />
          <el-option label="HTTPS" value="https" />
          <el-option label="SOCKS4" value="socks4" />
          <el-option label="SOCKS5" value="socks5" />
        </el-select>
      </div>
    </div>
    <!--    代理主机 ip-->
    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('session.hostAddress') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :placeholder="t('session.enterHostAddress')" v-model="proxyInfo.proxyIp"></el-input>
      </div>
    </div>
    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('session.portNumber') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :placeholder="t('session.enterPortNumber')" v-model="proxyInfo.proxyPort"></el-input>
      </div>
    </div>
    <!--    代理服务器验证开关-->
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('session.enableProxyAuth') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" size="default" v-model="proxyInfo.userVerifyStatus" />
      </div>
    </div>
    <!--    用户名 密码-->
    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('session.username') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :placeholder="t('session.enterUsername')" v-model="proxyInfo.username"></el-input>
      </div>
    </div>
    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('session.password') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :placeholder="t('session.enterPassword')" v-model="proxyInfo.password"></el-input>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, unref, watch } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { useMenuStore } from '@/stores/menuStore';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const menuStore = useMenuStore();
import { ElMessage } from 'element-plus';

const proxyInfo = ref({
  id: '',
  proxyStatus: 'false',
  proxyType: 'http',
  proxyIp: '',
  proxyPort: '',
  userVerifyStatus: 'false',
  username: '',
  password: ''
});
watch(
  () => menuStore.currentPartitionId,
  async (newValue, oldValue) => {
    if (newValue) {
      await getConfigInfo();
    }
  }
);
// 定义需要监听的字段
const propertiesToWatch = ['proxyStatus', 'proxyType', 'proxyIp', 'proxyPort', 'userVerifyStatus', 'username', 'password'];

let watchers = []; // 存储所有字段的监听器

// 初始化字段监听逻辑
const addWatchers = () => {
  removeWatchers(); // 确保不会重复绑定监听器
  watchers = propertiesToWatch.map((property) =>
    watch(
      () => unref(proxyInfo.value[property]),
      (newValue, oldValue) => {
        if (newValue !== '' && newValue !== oldValue) {
          handlePropertyChange(property, newValue);
        }
      }
    )
  );
};
// 自定义逻辑
const handlePropertyChange = async (property, value) => {
  const args = { key: property, value: value, id: proxyInfo.value.id };
  await ipc.invoke(ipcApiRoute.editProxyInfo, args);
  if (property === 'proxyStatus' || property === 'userVerifyStatus') {
    ElMessage({
      message: t('session.restartRequired'),
      type: 'warning',
      offset: 0
    });
  }
};
// 移除所有字段的监听器
const removeWatchers = () => {
  watchers.forEach((stopWatcher) => stopWatcher()); // 调用每个监听器的停止方法
  watchers = [];
};
const getConfigInfo = async () => {
  removeWatchers();
  try {
    const args = {
      partitionId: menuStore.currentPartitionId
    };
    const res = await ipc.invoke(ipcApiRoute.getProxyInfo, args);
    if (res.status) {
      Object.assign(proxyInfo.value, res.data); // 更新表单数据
    } else {
      console.log(res);
    }
  } catch (err) {
  } finally {
    addWatchers();
  }
};
onMounted(() => {
  getConfigInfo();
});
</script>
<style scoped lang="scss">
.proxy-config {
  width: 300px;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: var(--el-bg-color);
  :deep(.el-text) {
    --el-text-color: var(--el-text-color-primary);
  }
  :deep(.el-divider--horizontal) {
    margin: 14px 0;
  }
  .header-container {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    user-select: none;
    justify-content: flex-start;

    .header-title {
      display: flex;
      flex: 1;
      height: 30px;
    }
    .header-icon {
      height: 30px;
      display: flex;
      flex: 1;
    }
    .header-icon-add {
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1;
      cursor: pointer;
    }
  }

  .content-container-select {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-select__wrapper) {
      border-radius: 0;
    }
    :deep(.el-textarea__inner) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      display: flex;
      align-items: center;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      align-items: center;
      flex: 2;
    }
  }
  .content-container-input {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-input__wrapper) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      display: flex;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 2;
      align-items: center;
    }
  }
  .content-container-radio {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    .content-left {
      height: 50px;
      align-items: center;
      display: flex;
      flex: 2;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 1;
      align-items: center;
    }
  }
}
</style>

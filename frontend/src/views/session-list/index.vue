<template>
  <div class="session-list-container">
    <div class="session-list-header">
      <h2>{{ t('sessionList.title') }}</h2>
    </div>
    <div class="session-list-content">
      <el-empty :description="t('sessionList.noSessions')" :image-size="200" />
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>

<style scoped lang="scss">
.session-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: var(--el-bg-color-page);
}

.session-list-header {
  margin-bottom: 20px;

  h2 {
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.session-list-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
}
</style>

<template>
  <div class="global-translate-config">
    <el-tabs v-model="activeName" type="border-card">
      <!-- 标签页 -->
      <div class="component-tab">
        <el-tab-pane v-for="item in menuStore.translationRoute" :label="item.zhName" :name="item.name">
          <component :is="getTrcComponentByName(item.name)" :tabName="item.name" />
        </el-tab-pane>
      </div>
      <!-- 操作按钮 -->
      <div class="table-top-btn">
        <el-button @click="openAddDialog" type="primary">新增编码</el-button>
        <el-popconfirm placement="right" confirm-button-text="是" cancel-button-text="否" @confirm="batchDelete" title="确认删除所选编码？">
          <template #reference>
            <el-button type="danger" :disabled="selectedItems.length === 0">批量删除</el-button>
          </template>
        </el-popconfirm>
      </div>

      <div class="table-data">
        <!-- 公共部分：表格 -->
        <el-table height="100%" :data="tableData" border :key="activeName" @selection-change="handleSelectionChange">
          <el-table-column align="center" type="selection"></el-table-column>
          <el-table-column align="center" prop="code" width="90" label="编码"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" prop="zhName" label="中文名"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" prop="enName" label="英文名"></el-table-column>
          <el-table-column align="center" prop="youDao" label="有道编码" min-width="90"></el-table-column>
          <el-table-column align="center" prop="baidu" label="百度编码" min-width="90"></el-table-column>
          <el-table-column align="center" prop="huoShan" label="火山编码" min-width="90"></el-table-column>
          <el-table-column align="center" prop="xiaoNiu" label="小牛编码" min-width="90"></el-table-column>
          <el-table-column align="center" prop="google" label="谷歌编码" min-width="90"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" prop="timestamp" label="创建时间" min-width="100"></el-table-column>
          <el-table-column align="center" label="操作" min-width="160" fixed="right">
            <template #default="{ row }">
              <el-button @click="editItem(row)" type="primary">编辑</el-button>

              <el-popconfirm placement="left" confirm-button-text="是" cancel-button-text="否" @confirm="deleteItem(row)" title="确认删除该编码？">
                <template #reference>
                  <el-button type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-tabs>

    <!-- 新增对话框 -->
    <el-dialog v-model="addDialogVisible" title="新增编码" width="40%">
      <el-form :model="addForm" label-width="120px">
        <el-form-item label="本地语言编码">
          <el-input v-model="addForm.code" />
        </el-form-item>
        <el-form-item label="中文名称">
          <el-input v-model="addForm.zhName" />
        </el-form-item>
        <el-form-item label="英文名称">
          <el-input v-model="addForm.enName" />
        </el-form-item>
        <el-form-item label="有道翻译编码">
          <el-input v-model="addForm.youDao" />
        </el-form-item>
        <el-form-item label="百度翻译编码">
          <el-input v-model="addForm.baidu" />
        </el-form-item>
        <el-form-item label="火山翻译编码">
          <el-input v-model="addForm.huoShan" />
        </el-form-item>

        <el-form-item label="小牛翻译编码">
          <el-input v-model="addForm.xiaoNiu" />
        </el-form-item>
        <el-form-item label="谷歌翻译编码">
          <el-input v-model="addForm.google" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAdd">保存</el-button>
      </template>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑语言" width="40%">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="编码">
          <el-input v-model="editForm.code" />
        </el-form-item>
        <el-form-item label="中文名称">
          <el-input v-model="editForm.zhName" />
        </el-form-item>
        <el-form-item label="英文名称">
          <el-input v-model="editForm.enName" />
        </el-form-item>
        <el-form-item label="有道翻译编码">
          <el-input v-model="editForm.youDao" />
        </el-form-item>
        <el-form-item label="百度翻译编码">
          <el-input v-model="editForm.baidu" />
        </el-form-item>
        <el-form-item label="火山翻译编码">
          <el-input v-model="editForm.huoShan" />
        </el-form-item>
        <el-form-item label="小牛翻译编码">
          <el-input v-model="editForm.xiaoNiu" />
        </el-form-item>
        <el-form-item label="谷歌翻译编码">
          <el-input v-model="editForm.google" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, markRaw } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElTabs, ElTabPane, ElDialog, ElForm, ElFormItem, ElInput, ElMessage } from 'element-plus';
import GoogleTrc from '@/views/translate-config/GoogleTranslate.vue';
import YouDaoTrc from '@/views/translate-config/YouDaoTranslate.vue';
import BaiduTrc from '@/views/translate-config/BaiduTranslate.vue';
import HuoShanTrc from '@/views/translate-config/HuoShanTranslage.vue';
import XiaoNiuTrc from '@/views/translate-config/XiaoNiuTranslate.vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';

import { useMenuStore } from '@/stores/menuStore';

const menuStore = useMenuStore();

const trcComponent = ref([
  { name: 'google', component: markRaw(GoogleTrc) },
  { name: 'youDao', component: markRaw(YouDaoTrc) },
  { name: 'baidu', component: markRaw(BaiduTrc) },
  { name: 'huoShan', component: markRaw(HuoShanTrc) },
  { name: 'xiaoNiu', component: markRaw(XiaoNiuTrc) }
]);
const getTrcComponentByName = (name) => {
  const found = trcComponent.value.find((item) => item.name === name);
  return found ? found.component : null;
};

// 获取当前时间字符串
const getTimeStr = (date = new Date()) => {
  const pad = (n) => n.toString().padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` + `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};
// 当前激活的 tab 页
const activeName = ref('youDao');
const getTableData = async () => {
  const provider = activeName.value;
  if (!provider) return;
  const res = await ipc.invoke(ipcApiRoute.getLanguageList, { provider: provider });
  if (res.status) {
    tableData.value = res.data;
  }
};
watch(
  activeName,
  async (nVal, oVal) => {
    await getTableData();
  },
  { immediate: true }
);

// 表格数据
const tableData = ref([]);

// 选中的项
const selectedItems = ref([]);

// 新增对话框的显示状态
const addDialogVisible = ref(false);

// 编辑对话框的显示状态
const editDialogVisible = ref(false);

// 新增表单数据
const addForm = ref({
  provider: activeName.value,
  zhName: '',
  enName: '',
  code: '',
  google: '',
  youDao: '',
  baidu: '',
  huoShan: '',
  xiaoNiu: ''
});

// 编辑表单数据
const editForm = ref({
  id: null,
  zhName: '',
  enName: '',
  code: '',
  google: '',
  youDao: '',
  baidu: '',
  huoShan: '',
  xiaoNiu: ''
});

// 打开新增对话框
const openAddDialog = () => {
  addForm.value = { zhName: '', enName: '', code: '' }; // 清空表单
  addDialogVisible.value = true;
};

// 保存新增
const saveAdd = async () => {
  if (!addForm.value.code || !addForm.value.zhName) {
    ElMessage({
      message: `本地编码或中文名称不能为空`,
      type: 'error',
      offset: 40
    });
    return;
  }
  const newItem = {
    provider: activeName.value,
    zhName: addForm.value.zhName,
    enName: addForm.value.enName,
    code: addForm.value.code,
    youDao: addForm.value.youDao,
    baidu: addForm.value.baidu,
    huoShan: addForm.value.huoShan,
    xiaoNiu: addForm.value.xiaoNiu,
    google: addForm.value.google,
    timestamp: getTimeStr()
  };
  const res = await ipc.invoke(ipcApiRoute.addLanguage, newItem);
  if (res.status) {
    tableData.value.push(res.data);
    addDialogVisible.value = false;
    ElMessage({
      message: `添加成功！`,
      type: 'success',
      offset: 40
    });
  } else {
    ElMessage({
      message: `${res.message}`,
      type: 'error',
      offset: 40
    });
  }
};

// 编辑功能
const editItem = (row) => {
  editForm.value = { ...row };
  editDialogVisible.value = true;
};

// 保存编辑
const saveEdit = async () => {
  if (!editForm.value.code || !editForm.value.zhName) {
    ElMessage({
      message: `本地编码或中文名称不能为空`,
      type: 'error',
      offset: 40
    });
    return;
  }
  const index = tableData.value.findIndex((item) => item.id === editForm.value.id);
  if (index !== -1) {
    const args = {
      id: editForm.value.id,
      code: editForm.value.code,
      enName: editForm.value.enName,
      zhName: editForm.value.zhName,
      youDao: editForm.value.youDao,
      baidu: editForm.value.baidu,
      huoShan: editForm.value.huoShan,
      xiaoNiu: editForm.value.xiaoNiu,
      google: editForm.value.google
    };
    const res = await ipc.invoke(ipcApiRoute.editLanguage, args);
    if (res.status) {
      tableData.value[index] = { ...editForm.value };
      editDialogVisible.value = false;
      ElMessage({
        message: `编辑成功！`,
        type: 'success',
        offset: 40 // 设置距离顶部的位置，单位为像素
      });
    } else {
      ElMessage({
        offset: 40, // 设置距离顶部的位置，单位为像素
        message: `${res.message}`,
        type: 'error'
      });
    }
  }
};

// 删除功能
const deleteItem = async (row) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    const res = await ipc.invoke(ipcApiRoute.deleteLanguage, { id: row.id });
    if (res.status) {
      tableData.value.splice(index, 1);
    }
  }
};

// 批量删除功能
const batchDelete = async () => {
  const selectedIds = selectedItems.value.map((item) => item.id);
  for (let id of selectedIds) {
    const res = await ipc.invoke(ipcApiRoute.deleteLanguage, { id: id });
    if (res.status) {
      const index = tableData.value.findIndex((item) => item.id === id);
      if (index !== -1) {
        tableData.value.splice(index, 1);
      }
    }
  }
};

// 处理选中项
const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};
</script>

<style scoped lang="scss">
.global-translate-config {
  height: 100%;
  width: 100%;
  //padding: 10px;
  box-sizing: border-box;
  background-color: white;
  //border-radius: 5px;
  display: flex;
  flex-direction: column; /* 纵向排列 */
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
  :deep(.el-tabs__content) {
    height: calc(100vh - 120px);
  }
  .component-tab {
    //display: flex;
    height: 200px;
    width: 100%;
  }

  .table-top-btn {
    display: flex;
    height: 40px;
    width: 100%;
    align-items: center;
  }

  .table-data {
    display: flex;
    overflow: auto;
    flex: 1;
    height: calc(100vh - 350px);
    max-height: calc(100vh - 350px);
  }
}
</style>

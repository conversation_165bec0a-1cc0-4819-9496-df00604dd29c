<template>
  <div class="more-setting">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" type="border-card">
      <el-tab-pane label="翻译设置" name="translate">翻译设置</el-tab-pane>
      <el-tab-pane label="软件设置" name="platform"> 软件设置</el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const activeName = ref('translate');
const handleClick = (tab, event) => {
  console.log(tab, event);
};
</script>

<style scoped lang="scss">
.more-setting {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column; /* 纵向排列 */
  background-color: white;
  :deep(.el-tabs__content) {
    height: calc(100vh - 120px);
  }
}
</style>

<template>
  <div class="user-info w-full h-full p-4.5">
    <div class="w-full flex gap-2.5 h-12.5">
      <div class="w-50% h-full bg-#F5F7FA border-rd flex justify-center items-center">
        <i class="iconfont icon-kefu font-size-20px mr"></i>
        {{ seatInfoData?.seatName }}
      </div>
      <div class="w-50% h-full user-info-right border-rd flex justify-center items-center">
        <img v-if="seatInfoData?.memberType === 1||seatInfoData?.memberType === 2" alt="VIPIcon" class="w-7.25 h-7 mr-2"
             src="@/assets/images/index/VIPIcon.png"/>
        <img v-if="seatInfoData?.memberType === 3" alt="enterprise" class="w-7.25 h-7 mr-2"
             src="@/assets/images/index/enterprise-vip.png">
        {{ seatInfoData?.packageName }}
      </div>
    </div>
    <div class="w-full h-12.5 mt-2.5 px3 bg-#F5F7FA border-rd flex justify-between items-center">
      <span>到期时间</span>
      <span>{{ seatInfoData?.expiryTime }}</span>
    </div>
    <div class="w-full h-22 py4.5 mt-2.5 bg-#F5F7FA border-rd flex flex-col justify-center items-center">
      <span class="text-#595959">距离套餐到期还有几天？</span>
      <span class="font-bold mt">
        <span class="font-bold font-size-6">{{ ExpirationDate(seatInfoData?.expiryTime) }}</span>
        天
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {seatInfo} from '@/api/index/index.ts';
import {seatData} from "@/api/index/types.ts";

const seatInfoData = ref<seatData>();
const ExpirationDate = (expireTime: string) => {
  const now = new Date();
  const expireDate = new Date(expireTime);
  const diffTime = Math.abs(now.getTime() - expireDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
const getSeatInfo = async () => {
  const res = await seatInfo();
  seatInfoData.value = res?.data;
};
onMounted(() => {
  getSeatInfo();
});
</script>
<style lang="scss" scoped>
.user-info-right {
  background: linear-gradient(45deg, #baecca 0%, #eceda2 100%);
}
</style>

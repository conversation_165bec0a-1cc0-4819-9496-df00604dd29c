<template>
  <div class="carousel-container h-full w-full overflow-hidden">
    <el-carousel :interval="4000" arrow="never" height="100%">
      <el-carousel-item v-for="(item, index) in bannerList" :key="index">
        <a :href="item.jumpUrl" class="w-full h-full" target="_blank">
          <img :src="item.imageUrl" alt="carousel"
               class="carousel-img"/>
        </a>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script lang="ts" setup>
import {getBannerList} from '@/api/index/index.ts'

const bannerList = ref([])
const getBanner = () => {
  getBannerList().then(res => {
    bannerList.value = res
  })
}

onMounted(() => {
  getBanner()
})
</script>
<style lang="scss" scoped>
.carousel-container {
  width: 100%;
  height: 100%;
}

/* 确保轮播图容器有正确的高度 */
:deep(.el-carousel) {
  height: 100%;
}

:deep(.el-carousel__container) {
  height: 100%;
}

:deep(.el-carousel__item) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 链接样式 */
a {
  display: block;
  width: 100%;
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>

{"printWidth": 300, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "preserve", "jsxSingleQuote": false, "bracketSameLine": false, "trailingComma": "none", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "arrowParens": "always", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "auto"}
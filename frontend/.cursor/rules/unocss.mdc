---
description: 
globs: 
alwaysApply: true
---
---
description: 现代Web应用的unocss和UI组件最佳实践
globs: **/*.css, **/*.scss, **/*.vue, uno.config.ts
---

# unocss 最佳实践

## 项目设置
- 使用正确的unocss配置
- 正确配置主题扩展
- 设置正确的清除配置
- 使用正确的插件集成
- 配置自定义间距和断点
- 设置正确的配色方案

## 组件样式
- 优先使用工具类而非自定义CSS
- 在需要时使用@apply分组相关工具类
- 使用正确的响应式设计工具类
- 正确实现暗黑模式
- 使用正确的状态变体
- 保持组件样式一致性

## 布局
- 有效使用Flexbox和Grid工具类
- 实现正确的间距系统
- 在需要时使用容器查询
- 实现正确的响应式断点
- 使用正确的内边距和外边距工具类
- 实现正确的对齐工具类

## 排版
- 使用正确的字体大小工具类
- 实现正确的行高
- 使用正确的字体粗细工具类
- 正确配置自定义字体
- 使用正确的文本对齐
- 实现正确的文本装饰

## 颜色
- 使用语义化的颜色命名
- 实现正确的颜色对比度
- 有效使用不透明度工具类
- 正确配置自定义颜色
- 使用正确的渐变工具类
- 实现正确的悬停状态

## 组件
- 正确扩展组件
- 保持组件变体一致性
- 实现正确的动画
- 使用正确的过渡工具类
- 始终考虑可访问性

## 响应式设计
- 采用移动优先的方法
- 实现正确的断点
- 有效使用容器查询
- 正确处理不同屏幕尺寸
- 实现正确的响应式排版
- 使用正确的响应式间距

## 性能
- 使用正确的清除配置
- 尽量减少自定义CSS
- 使用正确的缓存策略
- 实现正确的代码分割
- 为生产环境优化
- 监控打包大小

## 最佳实践
- 遵循命名约定
- 保持样式组织有序
- 使用正确的文档
- 实现正确的测试
- 遵循可访问性指南
- 使用正确的版本控制
---
description: 
globs: 
alwaysApply: true
---
---
description: Vue.js 现代 Web 应用的最佳实践和模式
globs: **/*.vue, **/*.ts, components/**/*
---

# Vue.js 最佳实践

## 组件结构
- 使用组合式 API 而非选项式 API
- 保持组件小而专注
- 使用正确的 TypeScript 集成
- 实现适当的 props 验证
- 使用正确的 emit 声明
- 保持模板逻辑简洁

## 组合式 API
- 正确使用 ref 和 reactive
- 实现适当的生命周期钩子
- 使用可组合函数来复用逻辑
- 保持 setup 函数简洁
- 正确使用计算属性
- 实现适当的监听器

## 状态管理
- 使用 Pinia 进行状态管理
- 保持 store 模块化
- 使用正确的状态组合
- 实现适当的 actions
- 使用正确的 getters
- 正确处理异步状态

## 性能
- 正确使用组件懒加载
- 实现适当的缓存
- 使用正确的计算属性
- 避免不必要的监听器
- 正确使用 v-show 与 v-if
- 实现适当的 key 管理

## 路由
- 正确使用 Vue Router
- 实现适当的导航守卫
- 使用正确的路由元字段
- 正确处理路由参数
- 实现适当的懒加载
- 使用正确的导航方法

## 表单
- 正确使用 v-model
- 实现适当的验证
- 正确处理表单提交
- 显示适当的加载状态
- 使用正确的错误处理
- 实现适当的表单重置

## TypeScript 集成
- 使用正确的组件类型定义
- 实现适当的 prop 类型
- 使用正确的 emit 声明
- 处理适当的类型推断
- 使用正确的可组合类型
- 实现适当的 store 类型

## 测试
- 编写适当的单元测试
- 实现适当的组件测试
- 正确使用 Vue Test Utils
- 正确测试可组合函数
- 实现适当的模拟
- 测试异步操作

## 最佳实践
- 遵循 Vue 风格指南
- 使用正确的命名约定
- 保持组件组织有序
- 实现适当的错误处理
- 使用正确的事件处理
- 记录复杂的逻辑

## 构建与工具
- 使用 Vite 进行开发
- 配置适当的构建设置
- 使用正确的环境变量
- 实现适当的代码分割
- 使用正确的资源处理
- 配置适当的优化
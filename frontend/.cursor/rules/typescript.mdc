---
description: 
globs: 
alwaysApply: true
---
---
description: 现代Web开发的TypeScript编码规范和最佳实践
globs: **/*.ts, **/*.tsx, **/*.d.ts
---

# TypeScript 最佳实践

## 类型系统
- 对于对象定义，优先使用接口而不是类型
- 对联合类型、交叉类型和映射类型使用 type
- 避免使用 `any`，对于未知类型优先使用 `unknown`
- 使用严格的 TypeScript 配置
- 充分利用 TypeScript 内置的工具类型
- 对可复用的类型模式使用泛型

## 命名约定
- 类型名称和接口使用 PascalCase
- 变量和函数使用 camelCase
- 常量使用 UPPER_CASE
- 使用带有辅助动词的描述性名称（例如，isLoading, hasError）
- React 属性接口前缀使用 'Props'（例如，ButtonProps）

## 代码组织
- 将类型定义放在靠近使用它们的地方
- 当类型和接口被共享时，从专用的类型文件中导出
- 使用 barrel 导出（index.ts）来组织导出
- 将共享类型放在 `types` 目录中
- 将组件属性与其组件放在一起

## 函数
- 对公共函数使用显式返回类型
- 对回调和方法使用箭头函数
- 使用自定义错误类型实现适当的错误处理
- 对复杂的类型场景使用函数重载
- 优先使用 async/await 而不是 Promises

## 最佳实践
- 在 tsconfig.json 中启用严格模式
- 对不可变属性使用 readonly
- 利用可区分联合类型确保类型安全
- 使用类型守卫进行运行时类型检查
- 实现适当的空值检查
- 除非必要，避免使用类型断言
- 注释使用 TSDoc规范 以便在使用时获得良好的提示 （ @see TSDoc规范 https://tsdoc.org/ ）

## 错误处理
- 为特定领域的错误创建自定义错误类型
- 对可能失败的操作使用 Result 类型
- 实现适当的错误边界
- 使用带有类型化 catch 子句的 try-catch 块
- 正确处理 Promise 拒绝

## 模式
- 对复杂对象创建使用 Builder 模式
- 对数据访问实现 Repository 模式
- 对对象创建使用 Factory 模式
- 利用依赖注入
- 对封装使用 Module 模式
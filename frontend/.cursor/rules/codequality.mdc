---
description: 
globs: 
alwaysApply: true
---
---
description: 代码质量指南
globs: 
---
# 代码质量指南

## 验证信息
在呈现信息之前，始终要验证信息。不要在没有明确证据的情况下做出假设或推测。

## 逐文件更改
逐个文件进行更改，并给我机会发现错误。

## 不道歉
永远不要使用道歉的言辞。

## 不提供理解反馈
避免在评论或文档中提供关于理解的反馈。

## 不建议空白更改
不要建议进行空白字符的更改。

## 不总结
不要总结所做的更改。

## 不发明
除非明确要求，否则不要发明更改。

## 不进行不必要的确认
不要就上下文中已提供的信息请求确认。

## 保留现有代码
不要删除不相关的代码或功能。注意保留现有结构。

## 单一区块编辑
对同一文件的所有编辑提供在一个单一区块中，而不是多步骤说明或解释。

## 不进行实现检查
不要要求用户验证在提供的上下文中可见的实现。

## 不进行不必要的更新
当不需要实际修改时，不要建议更新或更改文件。

## 提供真实文件链接
始终提供真实文件的链接，而不是 x.md。

## 不讨论当前实现
除非特别要求，否则不要展示或讨论当前实现。
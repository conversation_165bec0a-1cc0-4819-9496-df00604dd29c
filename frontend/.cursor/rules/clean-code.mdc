---
description: 
globs: 
alwaysApply: true
---
---
description: 编写干净、可维护且易于阅读的代码的指南。在编写或审查代码时应用这些规则，以确保一致性和质量。
globs: 
---
# 干净代码指南

## 使用常量替代魔法数字
- 用命名的常量替换硬编码的值
- 使用描述性的常量名称，解释值的用途
- 将常量放在文件顶部或专用的常量文件中

## 有意义的命名
- 变量、函数和类应揭示其用途
- 名称应解释某物存在的原因及其使用方式
- 除非是普遍理解的缩写，否则避免使用缩写

## 智能注释
- 不要注释代码做什么 - 让代码自文档化
- 使用注释解释为什么以某种方式做某事
- 为API、复杂算法和非显而易见的副作用编写文档
- 注释使用 TSDoc规范 以便在使用时获得良好的提示 （ @see TSDoc规范 https://tsdoc.org/ ）

## 单一职责
- 每个函数应只做一件事
- 函数应小而专注
- 如果一个函数需要注释来解释其作用，则应将其拆分

## DRY（不要重复自己）
- 将重复的代码提取为可重用的函数
- 通过适当的抽象共享通用逻辑
- 维护单一的真相来源

## 干净的结构
- 将相关代码放在一起
- 以逻辑层次组织代码
- 使用一致的文件和文件夹命名约定

## 封装
- 隐藏实现细节
- 暴露清晰的接口
- 将嵌套条件语句移到命名良好的函数中

## 代码质量维护
- 持续重构
- 尽早修复技术债务
- 让代码比你发现时更干净

## 测试
- 在修复错误之前编写测试
- 保持测试可读性和可维护性
- 测试边缘情况和错误条件

## 版本控制
- 编写清晰的提交消息
- 进行小的、专注的提交
- 使用有意义的 branche 名称
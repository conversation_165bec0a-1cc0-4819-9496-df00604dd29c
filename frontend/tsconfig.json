{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "preserve", "declaration": false, "declarationMap": false, "sourceMap": true, "outDir": "./dist", "isolatedModules": true, "useDefineForClassFields": true, "skipLibCheck": true, "noEmit": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "auto-imports.d.ts", "components.d.ts", "*.vue"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}
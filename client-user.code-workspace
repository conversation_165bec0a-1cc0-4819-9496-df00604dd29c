{"folders": [{"name": "Root", "path": "."}, {"name": "Frontend", "path": "./frontend"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false, "volar.takeOverMode.enabled": true, "vue.codeActions.enabled": true, "vue.complete.casing.tags": "kebab", "vue.complete.casing.props": "camel", "files.associations": {"*.vue": "vue"}, "emmet.includeLanguages": {"vue-html": "html", "vue": "html"}}, "extensions": {"recommendations": ["Vue.volar", "Vue.vscode-typescript-vue-plugin", "antfu.unocss", "bradlc.vscode-tailwindcss"]}}
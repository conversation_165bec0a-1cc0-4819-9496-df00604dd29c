'use strict';
const { logger } = require('ee-core/log');
const { app, BrowserWindow } = require('electron')
class ContactInfoService {
  async getContactInfo(args,event) {
    const {userId,partitionId,platform} = args;
    if (!platform?.trim() && !userId?.trim() || (!partitionId?.trim() && !platform?.trim())) {
      return {status:false,message:'参数有误'}
    }
    if (partitionId?.trim()) {
      //获取窗口对象并查询是否有userid
      const view = app.viewsMap.get(partitionId)
      if (view && !view.webContents.isDestroyed()) {
        const nUserId = await view.webContents.executeJavaScript('getCurrentUserId()')
        if (nUserId?.trim()) {
          //更新数据
          const userInfo = await app.sdb.selectOne('contact_info', {userId:nUserId,platform:platform})
          if (userInfo) {
            let records = [];
            const followRecords = await app.sdb.select('follow_record', {userId:nUserId,platform:platform})
            if (followRecords?.length > 0) {
              records = followRecords;
            }
            return {status:true,message:'查询成功！',data:{userInfo:userInfo,records:records}}
          }else {
            //初始化用户信息
            await app.sdb.insert('contact_info',{userId:nUserId,platform:platform,phoneNumber:nUserId})
            return {status:true,message:'初始化用户信息成功！',data:{userInfo:{platform:platform,userId:nUserId},records:[]}}
            }
          }else {
          return {status:false,message:'请选择会话'}
        }
        }
      }
    const userInfo = await app.sdb.selectOne('contact_info',{userId:userId,platform:platform})
    if (userInfo) {
      let records = [];
      const followRecords = await app.sdb.select('follow_record', {userId:userId,platform:platform})
      if (followRecords?.length > 0) {
        records = followRecords;
      }
      return {status:true,message:'查询成功！',data:{userInfo:userInfo,records:records}}
    }else {
      //初始化用户信息
      await app.sdb.insert('contact_info',{userId:userId,platform:platform,phoneNumber:userId})
      const userInfo = await app.sdb.selectOne('contact_info',{userId:userId,platform:platform})
      return {status:true,message:'初始化用户信息成功！',data:{userInfo:userInfo,records:[]}}
    }
  }
  async updateContactInfo(args,event) {
    // 参数解构
    const { key, value, id } = args;
    // 参数校验
    if (!id) throw new Error('缺少必要参数：id');
    if (!key) throw new Error('缺少必要参数：key');
    if (typeof value === 'undefined') return;
    try {
      // 构建更新对象（使用动态属性名）
      const updateData = {
        [key]: value
      };
      // 执行更新
      await app.sdb.update(
          'contact_info',
          updateData,
          { id:id }
      );
      // 返回最新配置（可选）
      const updatedConfig = await app.sdb.selectOne('contact_info', { id:id });
      return {
        status: true,
        message:'更新成功',
        data: updatedConfig
      };
    } catch (error) {
      return {status:true,message:error.message};
    }
  }
  async getFollowRecord(args,event) {
    const {userId,platform} = args;
    if (!platform?.trim() && !userId?.trim()) {
      return {status:false,message:'参数有误'}
    }
    const records = await app.sdb.select('follow_record',{userId,platform});
    return {status:true,message:'查询成功',data:records};
  }
  async addFollowRecord(args,event) {
    const {userId,platform,content,timestamp} = args;
    if (!platform || !userId) {
      return {status:false,message:'参数有误'}
    }
    try{
      const id = await app.sdb.insert('follow_record',{userId:userId,platform:platform,content:content,timestamp:timestamp});
      if (id) {
        const row = {id:id,userId:userId,platform:platform,content:content,timestamp:timestamp};
        return {status:true,data:row,message:'添加成功'}
      }
      return {status:false,message:'添加失败'};
    }catch(err){
      return {status:false,message:`添加失败：${err.message}`};
    }
  }
  async updateFollowRecord(args,event) {
    const {id,content} = args;
    if (!id) return {status:false,message:'参数不能为空'}
    const count = await app.sdb.update('follow_record',{content:content},{id:id});
    return {status:true,message:'更新成功'};
  }
  async deleteFollowRecord(args,event) {
    const {id} = args;
    if (!id) {
      return {status:false,message:'参数有误'}
    }
    const count = await app.sdb.delete('follow_record',{id:id});
    return {status:true,message:'删除成功'};
  }
}
ContactInfoService.toString = () => '[class ContactInfoService]';

module.exports = {
  ContactInfoService,
  contactInfoService: new ContactInfoService()
};

'use strict';
const { logger } = require('ee-core/log');
const os = require('os')
const { app, BrowserWindow, WebContentsView ,session,shell} = require('electron');
const fs = require('fs').promises; // 使用 Promise 版本 API
const path = require('path');
const { generateUniquePartitionId,generateRandomString } = require('../utils/CommonUtils');
const {getMainWindow} = require("ee-core/electron");
class WindowService {

  async addSession (args,event) {
    const {platform,url,nickname} = args;
    try {
      //生成唯一分区id
      const partitionId = await generateUniquePartitionId();
      const createTime = this._getTimeFormat()
      if ('CustomWeb' === platform) {
        await app.sdb.insert('session_list',{partitionId:partitionId,windowStatus:'false',createTime:createTime,platform:platform,nickName:nickname,msgCount:0,onlineStatus:'false',webUrl:url});
        return {status:true,message:'新增成功',data: {partitionId}}
      }else {
        const webUrl = app.platforms.find(item => item.platform === platform)?.url;
        await app.sdb.insert('session_list',{partitionId:partitionId,windowStatus:'false',createTime:createTime,platform:platform,nickName:platform.toLowerCase(),msgCount:0,onlineStatus:'false',webUrl:webUrl});
        return {status:true,message:'新增成功',data: {partitionId}}
      }
    }catch(err){
      return {status:false,message:`添加失败：${err.message}`};
    }
  }

  async editSession (args,event) {
    const { id,windowId,windowStatus,onlineStatus,remarks,webUrl,avatarUrl,userName,msgCount,nickName} = args;
    // 参数验证
    if (!id) {
      return { status: false, message: '参数缺失，请检查 ID' };
    }
    // 创建要更新的字段对象
    const rows = { windowId,windowStatus,onlineStatus,remarks,webUrl,avatarUrl,userName,msgCount,nickName };
    // 过滤掉值为空的字段，避免更新无效字段
    Object.keys(rows).forEach(key => {
      if (rows[key] === undefined || rows[key] === null) {
        delete rows[key];
      }
    });
    // 执行更新
    try {
      const count = await app.sdb.update('session_list', rows, { id });
      // 检查更新结果
      if (count > 0) {
        return { status: true, message: `会话数据更新成功` };
      } else {
        return { status: false, message: `没有找到对应的会话配置，更新失败。` };
      }
    } catch (error) {
      return { status: false, message: `更新失败，系统错误：${error.message}` };
    }
  }

  //启动窗口
  async startSession (args,event) {
    const {platform, partitionId: inputPartitionId} = args; // 重命名解构变量
    try {
      const sessionObj = await app.sdb.selectOne("session_list",{
        platform: platform,
        partitionId: inputPartitionId // 使用重命名后的变量
      });
      if (!sessionObj) return {status:false,message:'没有这个会话记录！'}
      const oldView = app.viewsMap.get(inputPartitionId)
      if (oldView && !oldView.webContents.isDestroyed() && sessionObj.windowStatus ==='true') {
        return {status:true,message:'窗口已经启动！',}
      }
      // 使用新的变量名
      const sessionPartitionId = sessionObj.partitionId;
      const webUrl = sessionObj.webUrl;
      // 创建这个会话窗口
      const view = await this._createWebView(sessionPartitionId) // 使用新变量名
      let userAgent = sessionObj.userAgent;
      if (!userAgent) {
        // 设置 User-Agent
        userAgent = this.generateUserAgent();
        // userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.60 Safari/537.36'
      }
      view.webContents.setUserAgent(userAgent);
      app.viewsMap.set(sessionPartitionId, view) // 使用新变量名
      // view.webContents.openDevTools();
      const mainWin = getMainWindow()
      mainWin.contentView.addChildView(view);
      //加载配置以及处理代理配置信息
      this._loadConfig(view,sessionPartitionId,platform).then(async () => {
        try {
          await view.webContents.loadURL(webUrl, {userAgent: userAgent});
        }catch (err){
          logger.error('加载 URL 出错：',err.message);
        }
      })
      view.setVisible(false);
      await app.sdb.update("session_list",{windowStatus:'true',windowId:view.webContents.id,userAgent:userAgent},{platform:platform,partitionId:inputPartitionId});
      sessionObj.windowStatus = 'true'
      sessionObj.windowId=view.webContents.id
      return {status:true,message:'启动成功',data:sessionObj}
    } catch(err) {
      return {status:false,message:`启动失败：${err.message}`};
    }
  }
  //获取所有窗口会话信息
  async getSessions (args,event) {
    const {platform} = args;
    try {
      const sessions = await app.sdb.select("session_list",{platform:platform});
      return {status:true,message:'查询成功',data:{sessions:sessions}};
    }catch(err){
      return {status:false,message:`查询失败：${err.message}`};
    }
  }
  async getSessionByPartitionId (args,event) {
    const {partitionId,platform} = args;
    try {
      const session = await app.sdb.selectOne("session_list",{partitionId:partitionId});
      return {status:true,message:'查询成功',data:{session:session}};
    }catch(err){
      return {status:false,message:`查询失败：${err.message}`};
    }
  }
  //设置会话窗口位置
  async setWindowLocation (args,event) {
    const {x,y,width,height} = args;
    const location = {x:x, y:y,width:width,height:height};
    try {
      //获取所有窗口视图
      app.viewsMap.forEach((view, key) => {
        if (view && !view.webContents.isDestroyed()) {
          this._setViewLocation(view,location)
        }
      });
      return {status:true,message:'设置成功'};
    }catch(err){
      return {status:false,message:`设置失败：${err.message}`};
    }
  }
  //隐藏会话窗口
  async hiddenSession (args,event) {
    try {
      //获取所有窗口视图
      app.viewsMap.forEach((view, key) => {
        if (view && !view.webContents.isDestroyed()) {
          view.setVisible(false);
        }
      });
      return {status:true,message:'操作成功'};
    }catch(err){
      return {status:false,message:`操作失败：${err.message}`};
    }
  }
  async showSession (args,event) {
    const {platform,partitionId} = args;
    try {
      const sessionObj = await app.sdb.selectOne("session_list",{platform:platform,partitionId:partitionId});
      if (!sessionObj) return {status:false,message:'暂无当前会话记录！'}
      let view = app.viewsMap.get(partitionId)
      if (view && !view.webContents.isDestroyed()) {
        await this.hiddenSession()
        view.setVisible(true);
        return {status:true,message:'操作成功！'};
      }else {
        return {status:true,message:'会话不存在！请启动！'};
      }
    }catch(err){
      return {status:false,message:`操作失败：${err.message}`};
    }
  }
  async closeSession (args,event) {
    const {platform,partitionId} = args;
    try {
      const sessionObj = await app.sdb.selectOne("session_list",{partitionId:partitionId});
      if (!sessionObj) return {status:false,message:'暂无当前会话记录！'}
      await this._destroyView(partitionId);
      await app.sdb.update('session_list',{windowStatus:'false',onlineStatus:'false',msgCount:0,windowId:0},{partitionId:partitionId});
      sessionObj.windowStatus='false';
      sessionObj.onlineStatus='false';
      sessionObj.msgCount = 0;
      sessionObj.windowId = 0;
      return {status:true,message:'关闭成功',data:sessionObj}
    }catch(err){
      return {status:false,message:`关闭会话出错：${err.message}`};
    }
  }
  //刷新会话
  async refreshSession(args,event) {
    const {platform,partitionId} = args;
    const view = app.viewsMap.get(partitionId)
    if (view && !view.webContents.isDestroyed()) {
      // 刷新
      view.webContents.reload();
    }
    return {status:true,message:'刷新成功'}
  }
  async deleteSession (args,event) {
    const {partitionId} = args;
    try {
      const sessionObj = await app.sdb.selectOne("session_list",{partitionId:partitionId});
      if (!sessionObj) return {status:false,message:'暂无当前会话记录！'}
      await this._destroyView(partitionId);
      await this._deleteSessionAllData(partitionId)
      return {status:true,message:'删除会话成功！'}
    }catch(err){
      return {status:false,message:`删除会话出错：${err.message}`};
    }
  }
  //获取代理配置信息
  async getProxyInfo(args,event) {
    const {partitionId} = args;
    const config = await app.sdb.selectOne("proxy_config",{partitionId:partitionId});
    if (config) return {status:true,message:'查询成功',data:config};
    //初始化代理配置信息
    const row = {
      partitionId:partitionId,
      proxyStatus:'false',
      proxyType:'http',
      proxyIp:'',
      proxyPort:'',
      userVerifyStatus:'false',
      username:'',
      password:'',
    }
    const id = await app.sdb.insert('proxy_config',row);
    if (id) {
      row.id = id;
      return {status:true,message:'初始化代理配置成功',data:row};
    }
    return {status:false,message:'初始化代理配置出错'}
  }
  //修改窗口代理配置
  async editProxyInfo(args,event) {
    const { id,key,value} = args;
    // 参数校验
    if (!id) throw new Error('缺少必要参数：id');
    if (!key) throw new Error('缺少必要参数：key');
    if (typeof value === 'undefined') return;
    try {
      // 构建更新对象（使用动态属性名）
      const updateData = {
        [key]: value
      };
      // 执行更新
      await app.sdb.update('proxy_config', updateData, { id:id });
      return {status:true,message:'修改配置成功'}
    } catch (error) {
      logger.error('代理配置修改失败:', error);
      return {status:false,message:`修改配置出错：${error.message}`};
    }
  }
  async saveProxyInfo(args, event) {
    // 解构参数
    const {
      id,
      proxyStatus,
      proxyType,
      proxyIp,
      proxyPort,
      userVerifyStatus,
      username,
      password,
    } = args;

    // 参数校验
    if (!id) {
      throw new Error('缺少必要参数：id');
    }

    // 构建更新对象（动态生成需要更新的字段）
    const updateData = {};
    if (proxyStatus !== undefined) updateData.proxyStatus = proxyStatus;
    if (proxyType !== undefined) updateData.proxyType = proxyType;
    if (proxyIp !== undefined) updateData.proxyIp = proxyIp;
    if (proxyPort !== undefined) updateData.proxyPort = proxyPort;
    if (userVerifyStatus !== undefined) updateData.userVerifyStatus = userVerifyStatus;
    if (username !== undefined) updateData.username = username;
    if (password !== undefined) updateData.password = password;

    // 检查是否有需要更新的字段
    if (Object.keys(updateData).length === 0) {
      return { status: false, message: '未提供需要更新的字段' };
    }

    try {
      // 执行更新
      await app.sdb.update('proxy_config', updateData, { id });

      // 返回成功响应
      return { status: true, message: '代理配置更新成功' };
    } catch (error) {
      // 记录错误日志
      logger.error('代理配置更新失败:', error);

      // 返回错误响应
      return { status: false, message: `代理配置更新失败：${error.message}` };
    }
  }
  async openSessionDevTools (args,event) {
    const {partitionId,status} = args;
    const view = app.viewsMap.get(partitionId)
    if (view && !view.webContents.isDestroyed()) {
      if (status){
        view.webContents.openDevTools()
      }else view.webContents.closeDevTools()
    }
  }
  _setViewLocation (view,location) {
    view.setBounds({ x: location.x, y: location.y, width: location.width, height:location.height });
  }
  async _createWebView(partitionId) {
    const winSession = session.fromPartition(`persist:${partitionId}`);
    const sessionObj = await app.sdb.selectOne("session_list", {partitionId: partitionId});
    const platform = sessionObj.platform;
    let preloadPath = path.join(__dirname, `../preload/bridges`, `${platform}.js`)
    try {
      await fs.access(preloadPath); // 检查文件是否存在
    } catch (err) {
      preloadPath = path.join(__dirname, `../preload`, 'bridge.js')
    }
    const view = new WebContentsView({
      webPreferences: {
        session: winSession,
        sandbox: true,
        plugins: true,
        partition: `persist:${partitionId}`,
        nodeIntegration: false,
        contextIsolation: true,
        preload: preloadPath
      },
    });
    view.webContents.on('did-finish-load', () => {
      this._loadScriptFile(partitionId).then(() => {
      })

      // 应用页面缩放设置
      this._applyZoomSetting(view.webContents);
    });
    return view;
  }

  // 应用页面缩放设置到webContents
  _applyZoomSetting(webContents) {
    try {
      // 获取当前的页面缩放设置
      const pageZoomEnabled = app.pageZoom === true; // 只有明确设置为true时才启用缩放

      if (!webContents || webContents.isDestroyed()) {
        return;
      }

      if (pageZoomEnabled) {
        // 启用缩放 - 移除缩放限制
        webContents.setZoomFactor(1.0);
        // 移除之前可能设置的事件监听器
        webContents.removeAllListeners('zoom-changed');
        webContents.removeAllListeners('before-input-event');

        logger.info('新视图页面缩放已启用');
      } else {
        // 禁用缩放 - 固定缩放比例为1.0并阻止缩放事件
        webContents.setZoomFactor(1.0);

        // 移除之前的监听器，避免重复添加
        webContents.removeAllListeners('zoom-changed');
        webContents.removeAllListeners('before-input-event');

        // 监听缩放变化事件，强制重置为1.0
        webContents.on('zoom-changed', (event, zoomDirection) => {
          // 阻止缩放，始终保持1.0
          setTimeout(() => {
            if (!webContents.isDestroyed()) {
              webContents.setZoomFactor(1.0);
            }
          }, 0);
        });

        // 监听键盘事件来阻止缩放快捷键
        webContents.on('before-input-event', (event, input) => {
          // 阻止Ctrl+滚轮、Ctrl++、Ctrl+-等缩放快捷键
          if ((input.control || input.meta) && input.type === 'keyDown') {
            if (input.key === '+' || input.key === '=' || input.key === '-' || input.key === '0') {
              event.preventDefault();
            }
          }
        });

        // 注入CSS来禁用触摸缩放和其他缩放方式
        webContents.insertCSS(`
          html, body {
            zoom: 1 !important;
            transform: scale(1) !important;
            transform-origin: 0 0 !important;
          }
          * {
            zoom: 1 !important;
          }
        `).catch(() => {
          // 忽略CSS注入错误
        });

        logger.info('新视图页面缩放已禁用');
      }
    } catch (error) {
      logger.error('应用页面缩放设置失败:', error);
    }
  }

  _destroyView(partitionId) {
    const view = app.viewsMap.get(partitionId);
    // 检查 view 是否存在且未销毁
    if (view && !view.webContents.isDestroyed()) {
      view.setVisible(false)
      const mainWindow = getMainWindow();
      view.webContents.destroy()
      mainWindow.contentView.removeChildView(view);
      // 从 app.viewsMap 中删除该视图的引用
      app.viewsMap.delete(partitionId);
    } else {
      logger.warn(`未找到 ${partitionId} 的有效视图或视图已被销毁`);
    }
  }
  async _loadScriptFile(partitionId) {
    const view = app.viewsMap.get(partitionId);
    if (!view || view.webContents.isDestroyed()) {
      logger.info('会话已被销毁，不加载脚本！')
      return;
    }
    try {
      const sessionObj = await app.sdb.selectOne("session_list",{partitionId:partitionId});
      if (!sessionObj) return;
      const platform = sessionObj.platform;
      // 读取指定文件（示例路径：项目根目录的 scripts/ 目录）
      const scriptPath = path.join(__dirname, '../scripts', `${platform}.js`);
      const scriptContent = await fs.readFile(scriptPath, 'utf-8');
      // 注入并执行脚本
      await view.webContents.executeJavaScript(scriptContent);
      logger.info('execute jsCode successfully');
    } catch (err) {
      logger.error('脚本读取或执行失败:', err.message);
    }
  }
  async _loadConfig(view, partitionId, platform) {
    try {
      // await view.webContents.session.setProxy({mode: 'system'});
      // // 1. 清除 HTTP 缓存
      // await view.webContents.session.clearCache();
      //
      // // 2. 清除 DNS 缓存
      // await view.webContents.session.clearHostResolverCache();
      //
      // // 3. 关闭所有活跃连接
      // if (view.webContents.session.closeAllConnections) {
      //   await view.webContents.session.closeAllConnections();
      // }
      // 获取代理配置（优先按 partitionId 查询，其次按 platform）
      const config = await app.sdb.selectOne("proxy_config", {partitionId}) ||
          await app.sdb.selectOne("proxy_config", {partitionId: platform});

      // 未启用代理或配置无效时，使用系统代理设置
      if (!config || config.proxyStatus !== 'true' || !config.proxyIp || !config.proxyPort) {
        await view.webContents.session.setProxy({ mode: 'system' });
        logger.info(`[${partitionId}] Proxy: 使用系统代理设置`);
        return;
      }

      let proxyRules;
      const server = `${config.proxyIp}:${config.proxyPort}`;

      switch (config.proxyType) {
        case 'http':
          proxyRules = `http://${server}`;
          break;
        case 'https':
          // 注意：Electron 的 proxyRules 不直接区分 http 和 https 协议，
          // 它们都使用 'http=' 或 'https=' 前缀指定代理服务器。
          // 通常代理服务器本身会处理目标 URL 的协议。
          // 这里我们统一使用 http 规则，代理服务器需能处理 https 流量。
          // 如果需要为 https 单独指定代理，可以写成 `https=${credentials}${server}` 或组合规则。
          proxyRules = `http://${server}`; // 或者根据需要指定为 https=...
          logger.warn(`[${partitionId}] Proxy: HTTPS 代理将通过 HTTP 规则 (${proxyRules}) 进行。请确保代理服务器支持。`);
          break;
        case 'socks4':
          // SOCKS5 代理通常不需要用户名/密码在 URL 中，Electron 会通过其他机制处理（如果需要）
          // 但如果代理服务器实现要求在 URL 中包含，可以按需添加 credentials
          // Electron 文档倾向于 socks5=host:port 格式
          proxyRules = `socks4=socks4://${server}`;
          break;
        case 'socks5':
          // SOCKS5 代理通常不需要用户名/密码在 URL 中，Electron 会通过其他机制处理（如果需要）
          // 但如果代理服务器实现要求在 URL 中包含，可以按需添加 credentials
          // Electron 文档倾向于 socks5=host:port 格式
          proxyRules = `socks5=socks5://${server}`;
          break;
        default:
          logger.error(`[${partitionId}] Proxy: 不支持的代理类型 ${config.proxyType}`);
          await view.webContents.session.setProxy({ mode: 'system' }); // 回退到系统代理
          return;
      }

      const proxyConfig = {
        mode: 'fixed_servers',
        proxyRules: proxyRules,
        // proxyBypassRules: '<local>' // 可选：绕过本地地址的代理
      };
      // 应用代理配置
      await view.webContents.session.setProxy(proxyConfig);
      logger.info(`[${partitionId}] Proxy: 应用代理配置成功: ${JSON.stringify(proxyConfig)}`);
      // 代理认证处理（如果需要更精细的控制）
      // 注意：Electron 的 setProxy 通常会自动处理基于 URL 的凭据，但对于某些 SOCKS 或需要 NTLM/Kerberos 的场景，
      // 可能需要监听 'login' 事件进行手动认证。
      // 监听登录事件以处理代理身份验证
      if (config.userVerifyStatus === 'true' && config.username && config.password) {
        view.webContents.on('login', (event, request, authInfo, callback) => {
          event.preventDefault();
          if (authInfo.isProxy && authInfo.host === config.proxyIp && authInfo.port === Number(config.proxyPort)) {
            callback(config.username, config.password);
            logger.info(`会话${partitionId} 触发login事件`);
          }
        });
      }
      // 禁止打开新窗口 (这个逻辑似乎与代理配置不直接相关，但保留在这里)
      view.webContents.setWindowOpenHandler((detail) => {
        const url = detail.url;
        logger.info(`[${partitionId}] WindowOpenHandler: 尝试打开新窗口 ${url}, 将使用外部浏览器打开。`)
        // 调用系统默认浏览器打开
        shell.openExternal(url);
        // 拒绝 Electron 创建新窗口
        return { action: 'deny' };
      });


    } catch (err) {
      logger.error(`[${partitionId}] 配置代理出错:`, err);
      try {
        // 尝试回退到系统代理
        await view.webContents.session.setProxy({ mode: 'system' });
        logger.info(`[${partitionId}] Proxy: 因配置错误，已切换至系统代理设置`);
      } catch (fallbackErr) {
        logger.error(`[${partitionId}] Proxy: 回退到系统代理失败:`, fallbackErr);
      }
    }
  }
  generateUserAgent() {
    const chromeVersions = ['114.0.0.0', '115.0.0.0', '116.0.0.0', '117.0.0.0', '118.0.0.0', '119.0.0.0'];
    const platforms = {
      win: {
        os: 'Windows NT 10.0; Win64; x64',
        platform: 'Windows'
      },
      mac: {
        os: 'Macintosh; Intel Mac OS X 10_15_7',
        platform: 'macOS'
      },
      linux: {
        os: 'X11; Linux x86_64',
        platform: 'Linux'
      }
    };

    // 通过 Node.js 的 os 模块检测系统类型
    const platformMap = {
      win32: 'win',
      darwin: 'mac',
      linux: 'linux'
    };
    const currentPlatform = os.platform();
    const platformKey = platformMap[currentPlatform] || 'win32';

    // 获取对应平台的配置信息
    const selectedPlatform = platforms[platformKey];
    const randomChrome = chromeVersions[Math.floor(Math.random() * chromeVersions.length)];

    return `Mozilla/5.0 (${selectedPlatform.os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomChrome} Safari/537.36`;
  }
  _getTimeFormat() {
    const now = new Date();

    // 补零函数（确保两位数字）
    const padZero = (n) => String(n).padStart(2, "0");

    // 提取时间组件
    const month = padZero(now.getMonth() + 1),  // 月份从 0 开始
        day = padZero(now.getDate()),
        hours = padZero(now.getHours()),
        minutes = padZero(now.getMinutes());
    // 拼接目标格式
    return `${month}-${day} ${hours}:${minutes}`;
  }
  //删除会话相关的所有数据信息
  async _deleteSessionAllData (partitionId) {
    //会话记录表数据删除
    await app.sdb.delete('session_list',{partitionId:partitionId});
    //删除会话相关的翻译缓存
    await app.sdb.delete('translate_cache',{partitionId:partitionId});
  }

}
WindowService.toString = () => '[class WindowService]';

module.exports = {
  WindowService,
  windowService: new WindowService()
};

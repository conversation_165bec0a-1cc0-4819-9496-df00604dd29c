'use strict';

const path = require('path');
const { getBaseDir } = require('ee-core/ps');

/**
 * 默认配置
 */
module.exports = () => {
  return {
    openDevTools: false,
    singleLock: true,
    windowsOption: {
      // 更多属性，见文档：https://www.electronjs.org/zh/docs/latest/api/browser-window#new-browserwindowoptions
      title: 'A2C',
      width: 1200,
      height: 800,
      minWidth: 1200,
      minHeight: 800,

      webPreferences: {
        // webSecurity: false, // 如果需要跨域，请打开注释
        contextIsolation: false, // false -> 可在渲染进程中使用electron的api，true->需要bridge.js(contextBridge)
        nodeIntegration: true, // node模块
        //preload: path.join(getElectronDir(), 'preload', 'bridge.js'),
        // allowRunningInsecureContent: true, // 允许https加载http的资源
      },
      titleBarStyle: 'hidden', // 隐藏窗口标题栏
      trafficLightPosition: { x: -20, y: -20 },// 隐藏Mac窗口的红绿灯
      frame: false, // 隐藏窗口边框
      show: false,
      icon: path.join(getBaseDir(), 'public', 'images', 'logo-32.png')
    },
    logger: {
      rotator: 'day',
      level: 'INFO',
      outputJSON: false,
      appLogName: 'ee.log',
      coreLogName: 'ee-core.log',
      errorLogName: 'ee-error.log',
    },
    remote: {
      enable: false,
      url: ''
    },
    socketServer: {
      enable: false,
      port: 7070,
      path: "/socket.io/",
      connectTimeout: 45000,
      pingTimeout: 30000,
      pingInterval: 25000,
      maxHttpBufferSize: 1e8,
      transports: ["polling", "websocket"],
      cors: {
        origin: true,
      },
      channel: 'socket-channel'
    },
    httpServer: {
      enable: false,
      https: {
        enable: false,
        key: '/public/ssl/localhost+1.key',
        cert: '/public/ssl/localhost+1.pem'
      },
      host: '127.0.0.1',
      port: 7071,
    },
    mainServer: {
      indexPath: '/public/dist/index.html',
      channelSeparator: '/',
    }
  }
}

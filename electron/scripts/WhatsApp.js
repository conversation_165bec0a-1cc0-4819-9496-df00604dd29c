const ipc = window.electronAPI;
let userInfo = {};
let trcConfig = {}
//========================用户基本信息获取开始============
const getCurrentUserId = (item) => {
    const element = document.querySelector('div#main');
    let userNum = null;
    // 获取 React 的属性对象
    let reactProps = null;
    for (let key in element) {
        if (key.startsWith('__reactProps$')) {
            reactProps = element[key];
            break;
        }
    }
    if (reactProps && reactProps.children && reactProps.children.key) {
        userNum = reactProps.children.key;
        userNum = userNum.replace(/@.*/, '');
    }
    return userNum;
}
const onlineStatusCheck = ()=> {
    setInterval(async () => {
        const element = localStorage["last-wid-md"]
        if (element) {
            const phoneNumber = element.split(":")[0].replace(/"/g, "");
            const profilePicUrl = localStorage.WACachedProfilePicEURL;
            const avatarUrl = profilePicUrl.replace(/^"|"$/g, '');
            const msgCount = await getNewMsgCount()
            const args = {
                platform: 'WhatsApp',
                onlineStatus: true,
                avatarUrl: avatarUrl,
                nickName:'',
                phoneNumber:phoneNumber,
                userName:phoneNumber,
                msgCount:msgCount
            }
            ipc.loginNotify(args);
            userInfo = args;
        }else {
            const args = {
                platform: 'WhatsApp',
                onlineStatus: false,
                avatarUrl: '',
                nickName:'',
                phoneNumber:'',
                userName:'',
                msgCount:0
            }
            ipc.loginNotify(args);
            userInfo = args;
        }
    }, 3000); // 每隔5000毫秒（3秒）调用一次
}
const getNewMsgCount = () => {
    try {
        let newMsgCount = 0; // 累加消息数量

        // 获取所有符合条件的 `div[role='listitem']` 节点
        const chatNodes = document.querySelectorAll("div[role='listitem']");

        // 遍历所有节点并提取 `span` 的值
        chatNodes.forEach((node) => {
            // 定义目标 `span` 节点的类选择器
            const targetClassSelector = `
            .x1rg5ohu.x173ssrc.x1xaadd7.x682dto.x1e01kqd.x12j7j87
            .x9bpaai.x1pg5gke.x1s688f.xo5v014.x1u28eo4.x2b8uid
            .x16dsc37.x18ba5f9.x1sbl2l.xy9co9w.x5r174s.x7h3shv
        `.replace(/\s+/g, ''); // 移除空格确保选择器格式正确
            const span = node.querySelector(`span${targetClassSelector}[aria-label]`); // 查询特定类的 `span`
            const svg = node.querySelector('span[data-icon="muted"]');
            if (svg) return;
            if (span) {
                const textContent = span.textContent.trim(); // 获取文本内容并去掉空格
                const value = parseInt(textContent, 10); // 转换为整数
                if (!isNaN(value)) {
                    newMsgCount += value; // 累加有效值
                }
            }
        });

        // 直接返回消息数量
        return newMsgCount;
    } catch (e) {
        console.error("获取新消息数量发生错误：", e.message);
        return 0; // 发生错误时返回 0
    }
};
onlineStatusCheck();
//中文检测
const containsChinese = (text)=> {
    const regex = /[\u4e00-\u9fa5]/;  // 匹配中文字符的正则表达式
    return regex.test(text);  // 如果包含中文字符返回 true，否则返回 false
}
const sendMsg = () => {
    let sendButton = getSendBtn();
    if (sendButton) {
        sendButton.click();
    }
}
const sendTranslate = async (text,to)=>{
    if (text && text.trim()) {
        const route = trcConfig.translateRoute;
        const mode = trcConfig.mode;
        styledTextarea.setContent('翻译中...')
        styledTextarea.setIsProcessing(true);
        // console.log('发送翻译请求：')
        const res = await ipc.translateText({route: route, text: text, to: to,isFilter:'true',mode:mode});
        if (res.status) {
            //调用ipc获取翻译结果
            styledTextarea.setContent(res.data);
            styledTextarea.setTranslateStatus(true)
            styledTextarea.setIsProcessing(false);
        }else {
            styledTextarea.setContent(res.message);
            styledTextarea.setTranslateStatus(false)
            styledTextarea.setIsProcessing(false);
        }
    }
}
const createStyledTextarea = () => {
    const container = document.createElement('div');
    Object.assign(container.style, {
        display: 'flex',
        flexDirection: 'column',
    });
    container.id = 'custom-translate-textarea';
    container.classList.add('x1bmpntp');
    container.style.boxSizing = 'border-box'
    // 标题元素
    const label = document.createElement('span');
    Object.assign(label.style, {
        fontSize: '12px',
        color: 'green',
        marginLeft: '70px',
        marginTop: '10px',
        marginBottom: '10px',
    });
    label.textContent = ''
    // 文本域容器
    const textareaWrapper = document.createElement('div');
    textareaWrapper.style.overflowY = 'auto';
    textareaWrapper.style.maxHeight = '100px';
    const textarea = document.createElement('textarea');
    textarea.classList.add("x1bmpntp")
    textarea.classList.add("_amk4")
    Object.assign(textarea.style, {
        fontSize: '12px',
        opacity: '0.7',  // 添加这行来设置透明度
        width: '100%',
        border: 'none',
        resize: 'none',
        overflow: 'hidden',
        boxSizing: 'border-box',
        paddingLeft: '70px',
        paddingRight: '60px',
        outline: 'none',
        overflowY: 'auto',
        pointerEvents: 'none', // 禁用交互
    });
    textarea.tabIndex = -1;  // 禁用键盘聚焦
    textarea.value = '...';
    // 自动高度调整
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // 组装元素
    textareaWrapper.appendChild(textarea);
    container.appendChild(label);
    container.appendChild(textareaWrapper);
    //翻译状态
    let translateStatus = false;
    let isProcessing = false;
    //初始化属性数据
    const initData = () =>{
        translateStatus = false;
        isProcessing = false;
        textarea.value = '...';
        // 手动触发高度调整
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
    }
    // 暴露设置方法
    const setContent = (content) => {
        if (content) {
            textarea.value = content;
            // 手动触发高度调整
            const event = new Event('input', { bubbles: true });
            textarea.dispatchEvent(event);
        }
    };
    const getContent = () => {
        return textarea.value;
    };
    const setTitle = (title) => {
        if (title) label.textContent = title;
    };
    const setTranslateStatus = (status) => {
        translateStatus = status;
    };
    const getTranslateStatus = () => {
        return translateStatus; // 直接返回状态值
    };
    const setIsProcessing = (status) => {
        isProcessing = status;
    };
    const getIsProcessing = () => {
        return isProcessing; // 直接返回状态值
    };

    return {
        initData,
        container,    // DOM节点
        setTitle,   //设置翻译线路
        setContent,    // 设置内容的方法
        setTranslateStatus,//设置翻译状态
        getTranslateStatus,
        getIsProcessing,
        setIsProcessing,
        getContent
    };
};
const styledTextarea = createStyledTextarea()
const addTranslatePreview = ()=>{
    const footerDiv = document.querySelector("#main > footer")
    const msgDiv = footerDiv.childNodes[0]
    if (msgDiv) {
        // 创建父容器
        const container = document.createElement('div');
        msgDiv.parentNode.insertBefore(container, msgDiv);
        container.appendChild(styledTextarea.container);
        container.appendChild(msgDiv);
    }
}
const addTranslateListener = () => {
    // 获取元素
    const editableDiv = document.querySelector('footer div[aria-owns="emoji-suggestion"][contenteditable="true"]')

    // 防抖函数
    const debounce = (func, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId); // 清除之前的定时器
            timeoutId = setTimeout(() => func.apply(this, args), delay); // 设置新的定时器
        };
    };

    // 处理输入事件的逻辑
    const handleInput = async () => {
        const flag = trcConfig.translatePreview;
        const textContent = getMsgText(editableDiv)
        const sendStatus = trcConfig.sendTranslateStatus;
        if(textContent === '' || !textContent) {
            styledTextarea.setContent('...')
            return;
        }
        const isProcessing = styledTextarea.getIsProcessing();
        if (flag && flag === 'true' && sendStatus === 'true' && isProcessing === false) {
            const to = trcConfig.sendTargetLanguage;
            styledTextarea.setTranslateStatus(false)
            await sendTranslate(textContent,to);
        }
    };
    // 创建防抖后的 handleInput 函数
    const debouncedHandleInput = debounce(handleInput, 500);

    let isProcessing = false;
    editableDiv.addEventListener('keydown', async (event) => {
        debouncedHandleInput()
        if (event.key === 'Enter' && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            if (isProcessing) return;
            const sendTranslateStatus = trcConfig.sendTranslateStatus;
            const sendPreview = trcConfig.translatePreview;
            const textContent = getMsgText(editableDiv);
            const from = trcConfig.sendSourceLanguage;
            const to = trcConfig.sendTargetLanguage;
            const route = trcConfig.translateRoute;
            const mode = trcConfig.mode;
            const args = {text:textContent,from:from,to: to,route:route,mode:mode};
            if (sendPreview === 'true'&& sendTranslateStatus === 'true') {
                const status = styledTextarea.getTranslateStatus()
                const isProcess = styledTextarea.getIsProcessing()
                if (status === true && isProcess === false) {
                    const translateText = styledTextarea.getContent();
                    // 获取所有需要翻译的文本元素
                    let msgElements = document.querySelectorAll('span.selectable-text.copyable-text[data-lexical-text="true"]');
                    //判断发送的内容是否全是表情
                    if (editableDiv.querySelector('span') && msgElements.length<=0) {
                        sendMsg()
                        return;
                    }
                    await inputMsg(translateText)
                    // 发送消息，确保翻译完成后再发送
                    sendMsg();
                    styledTextarea.setTranslateStatus(false)
                    styledTextarea.setContent('...')
                }
            }else if (textContent && textContent !=='' && sendTranslateStatus === 'true') {
                isProcessing = true;
                styledTextarea.setContent('翻译中...')
                const res = await ipc.translateText(args)
                if (res.status) {
                    const translateText = res.data;
                    await inputMsg(translateText)
                    // 发送消息，确保翻译完成后再发送
                    sendMsg();
                    isProcessing = false;
                }else {
                    styledTextarea.setContent(res.message);
                    isProcessing = false;
                }
            }else {
                sendMsg();
            }
        }
    },true);
    // 监听父容器的事件
    document.body.addEventListener('click', function(event) {
        // 通过特征匹配目标元素
        const target = event.target.closest('span[data-icon="send"]') || event.target.closest('span[data-icon="wds-ic-send-filled"]');
        if (target && event.isSimulated !== true) {
            const status = styledTextarea.getTranslateStatus()
            const isProcess = styledTextarea.getIsProcessing()
            const sendTranslateStatus = trcConfig.sendTranslateStatus;
            // console.log(`发送按钮被点击：status: ${status} isProcess: ${isProcess} sendTranslateStatus: ${sendTranslateStatus}`)
            if (isProcess === true) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            }else {
                if (sendTranslateStatus === 'true' && status === true) {
                    const translateText = styledTextarea.getContent();
                    inputMsg(translateText).then(()=>{
                        styledTextarea.setTranslateStatus(false)
                    })
                }else {
                    const to = trcConfig.sendTargetLanguage;
                    styledTextarea.setTranslateStatus(false)
                    let textContent = getMsgText(editableDiv)
                    // console.log('翻译文本',textContent)
                    sendTranslate(textContent,to);
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                }
            }
        }
    });
};
const updateConfigInfo = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'WhatsApp', userId: currentUserId || ''};
    const res = await ipc.getTranslateConfig(args)
    if (res.status) {
        styledTextarea.setTitle(res.data.zhName);
        trcConfig = res.data;
    }else {
        trcConfig = {};
        console.error('获取配置信息失败：',res);
    }
}

//会话列表切换触发函数
const sessionChange = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'WhatsApp', userId: currentUserId};
    ipc.infoUpdate(args)
    await updateConfigInfo()
    const myNode = document.getElementById('custom-translate-textarea')
    if (!myNode) {
        addTranslatePreview();
        addTranslateListener();
    }
    styledTextarea.initData()
}
const debouncedSessionChange = debounce(sessionChange,200);

const getMsgText = (node) => {
    // 获取所有 span 元素
    const spans = node.querySelectorAll('span');
    // 用于存储每一行的内容
    let content = [];
    spans.forEach(span => {
        // 获取 span 的文本内容并去除前后空格
        const text = span.textContent.trim();
        // 如果内容为空，则添加空字符串，否则添加文本内容
        content.push(text === '' ? '' : text);
    });
    // 将内容数组拼接成一个字符串，用换行符分隔
    return content.join('\n');
};

const monitorMainNode = ()=> {
    // 监听整个 body 的 DOM 变化，等待 #main 节点的出现
    const observer = new MutationObserver(async (mutationsList, observer) => {
        for (let mutation of mutationsList) {
            if (mutation.type === 'childList') {
                //设置授权码
                // 检查是否已经存在 id="main" 的节点
                const mainNode = document.getElementById('pane-side');
                if (mainNode) {
                    // 停止对 body 的观察，避免不必要的性能开销
                    observer.disconnect();
                    // 开始监听 #main 节点的子节点变化
                    observePaneSide(mainNode);
                }
            }
        }
    });

    // 监听左侧消息列表切换
    const observePaneSide = (paneSideNode) => {
        const observer = new MutationObserver(async (mutationsList) => {
            for (const mutation of mutationsList) {
                // 确保是属性变化事件，并且是 aria-selected 属性
                if (mutation.type === 'attributes' && mutation.attributeName === 'aria-selected') {
                    const targetNode = mutation.target;
                    // 检查 aria-selected 属性值
                    if (targetNode.getAttribute('aria-selected') === 'true') {
                        observeChatChanges();
                        debouncedSessionChange()
                    }
                }
            }
        });
        // 配置 MutationObserver，监听 aria-selected 属性变化
        const config = { attributes: true, subtree: true, attributeFilter: ['aria-selected'] };
        // 监听 pane-side 节点
        observer.observe(paneSideNode, config);
    }

    // 每次切换会话后重新获取监听消息列表处理消息翻译
    const observeChatChanges = () => {
        const chatContainer = document.querySelector("#app");
        if (chatContainer) {
            const observer = new MutationObserver((mutations) => {
                observer.disconnect(); // 暂时断开观察器以避免循环触发
                debouncedAddTranslateButtonToAllMessages()
                observer.observe(chatContainer, {
                    childList: true,
                    subtree: true,
                });
            });
            observer.observe(chatContainer, {
                childList: true,
                subtree: true,
            });
        }
    };
    /**
     * 为所有消息添加翻译按钮
     */
    const addTranslateButtonToAllMessages = async () => {
        // 选择发送和接收的消息元素
        const messageElements = document.querySelectorAll("div[role='row'] .message-out, div[role='row'] .message-in");
        for (let i = messageElements.length - 1; i >= 0; i--) {
            const msgSpan = messageElements[i].querySelector('span[dir="ltr"]');
            if (msgSpan) {
                const flag = msgSpan.hasAttribute('data-translated');
                if (flag === false) {
                    //添加属性表示已经处理过翻译
                    msgSpan.setAttribute('data-translated', 'yes');
                    await createTranslateButtonForMessage(msgSpan);
                }
            }
        }
    };
    //为每条消息创建翻译按钮
    const createTranslateButtonForMessage = async ( msgSpan) => {
        let text = getMsgText(msgSpan);
        if (!text) return;
        const translateDiv = document.createElement("div");
        translateDiv.style.cssText = `
        min-height: 20px;
        justify-content: space-between;
        margin-top: 5px;
        border-top:1px dashed var(--message-primary);
        color:var(--secondary);
        display: flex;`;
        const leftDiv = document.createElement("div");
        leftDiv.style.cssText = `
            min-height: 20px;
            font-size:12px;
            color:var(--WDS-content-action-emphasized);`;
        const rightDiv = document.createElement("div");
        rightDiv.style.cssText = `
            cursor: pointer;
            width: 20px;
            height: 20px;
            user-select: none;
            margin-left:5px;
        `;
        rightDiv.textContent = '🔄'
        rightDiv.addEventListener('click', async (e) => {
            let text = getMsgText(msgSpan);
            text = text.replace(/\n/g, '<br>');
            leftDiv.style.color = 'var(--WDS-content-action-emphasized)';
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,refresh:'true',mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        });
        // 组装结构
        translateDiv.appendChild(leftDiv);
        translateDiv.appendChild(rightDiv);
        // 插入到消息元素右侧
        msgSpan.appendChild(translateDiv);

        const receiveTranslateStatus = trcConfig.receiveTranslateStatus;
        if (receiveTranslateStatus === 'true') {
            let text = getMsgText(msgSpan);
            text = text.replace(/\n/g, '<br>');
            leftDiv.style.color = 'var(--WDS-content-action-emphasized)';
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        }
    };
    const debouncedAddTranslateButtonToAllMessages = debounce(
        addTranslateButtonToAllMessages,
        200
    );
    // 开始观察 body 的子节点变化
    observer.observe(document.body, { childList: true, subtree: true });
}
/**
 * 函数防抖，用于优化频繁触发的操作
 * @param {Function} func 需要防抖的函数
 * @param {number} delay 防抖延迟时间（毫秒）
 */
function debounce (func, delay){
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), delay);
    };
};
monitorMainNode()
function getSendBtn (){
    return document.querySelector('footer span[data-icon="send"]')?.parentNode
        || document.querySelector('footer span[data-icon="wds-ic-send-filled"]')?.parentNode;
}
//快捷回复相关部分
async function inputMsg(translation) {
    // 从 footer 开始查找输入框
    const footer = document.querySelector('footer._ak1i');
    if (!footer) {
        console.error('未找到输入框容器');
        return;
    }
    // 查找富文本输入框
    const richTextInput = footer.querySelector('.lexical-rich-text-input div[contenteditable="true"]');
    // 1. 检测操作系统
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
    // 2. 聚焦输入框
    richTextInput.focus();
    // 3. 模拟 Ctrl+A/Command+A
    const selectAll = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: !isMac,
        metaKey: isMac,
        bubbles: true
    });
    richTextInput.dispatchEvent(selectAll);
    // 4. 模拟退格键
    const backspace = new KeyboardEvent('keydown', {
        key: 'Backspace',
        code: 'Backspace',
        bubbles: true
    });
    richTextInput.dispatchEvent(backspace);
    // 7. 触发输入事件
    const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: translation
    });
    richTextInput.dispatchEvent(inputEvent);
}
const quickReply = async (args)=>{
    const {type,text} = args;
    if (type === 'input') {
        await inputMsg(text);
    }
    if (type === 'send') {
        await inputMsg(text);
        let sendButton = getSendBtn()
        if (sendButton) {
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            event.isSimulated = true; // 标记为模拟事件
            sendButton.dispatchEvent(event);
        }
    }
}

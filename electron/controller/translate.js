'use strict';

const { logger } = require('ee-core/log');
const {translateService} = require("../service/translate");
const {app} = require("electron");
// Imports the Google Cloud client library
const {Translate} = require('@google-cloud/translate').v2;
/**
 * 翻译配置
 * @class
 */
class TranslateController {


    async getConfigInfo(args,event) {
        return await translateService.getConfigInfo(args,event);
    }

    async changeAloneStatus(args,event) {
        return await translateService.changeAloneStatus(args,event);
    }

    async updateTranslateConfig(args,event) {
        return await translateService.updateTranslateConfig(args,event);
    }

    async getLanguageList(args,event) {
        return await translateService.getLanguageList(args,event);
    }

    async addLanguage(args,event) {
        return await translateService.addLanguage(args,event);
    }
    async deleteLanguage(args,event) {
        return await translateService.deleteLanguage(args,event);
    }
    async editLanguage(args,event) {
        return await translateService.editLanguage(args,event);
    }
    async addTranslateRoute(args,event) {
        return await translateService.addTranslateRoute(args,event);
    }
    async editTranslateRoute(args,event) {
        return await translateService.editTranslateRoute(args,event);
    }
    async getRouteConfig(args,event) {
        return await translateService.getRouteConfig(args,event);
    }
    async getRouteList(args, event) {
        return await translateService.getRouteList(args,event);
    }
    async testRoute(args,event) {
        return await translateService.testRoute(args,event);
    }

    async translateText(args,event) {
        return await translateService.translateText(args,event);
    }

    async clearSessionCache(args,event) {
        return await translateService.clearSessionCache(args,event);
    }

}
TranslateController.toString = () => '[class TranslateController]';

module.exports = TranslateController;

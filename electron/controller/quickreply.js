'use strict';

const { logger } = require('ee-core/log');
const {quickReplyService} = require("../service/quickreply");

/**
 * 快捷回复 api
 * @class
 */
class QuickReplyController {

    async getGroups(args,event) {
        return await quickReplyService.getGroups(args,event);
    }
    async getContentByGroupId(args,event) {
        return await quickReplyService.getContentByGroupId(args,event);
    }
    async addGroup(args,event) {
        return await quickReplyService.addGroup(args,event);
    }
    async editGroup(args,event) {
        return await quickReplyService.editGroup(args,event);
    }
    async deleteGroup(args,event) {
        return await quickReplyService.deleteGroup(args,event);
    }
    async addReply(args,event) {
        return await quickReplyService.addReply(args,event);
    }
    async editReply(args,event) {
        return await quickReplyService.editReply(args,event);
    }
    async deleteReply(args,event) {
        return await quickReplyService.deleteReply(args,event);
    }
    async deleteAllReply(args,event) {
        return await quickReplyService.deleteAllReply(args,event);
    }
}
QuickReplyController.toString = () => '[class QuickReplyController]';

module.exports = QuickReplyController;

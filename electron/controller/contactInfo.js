'use strict';

const { logger } = require('ee-core/log');
const {contactInfoService} = require("../service/contactInfo");

/**
 * 联系人信息api
 * @class
 */
class ContactInfoController {

    async getContactInfo(args,event) {
        return await contactInfoService.getContactInfo(args,event);
    }
    async updateContactInfo(args,event) {
        return await contactInfoService.updateContactInfo(args,event);
    }

    async getFollowRecord(args,event) {
        return await contactInfoService.getFollowRecord(args,event);
    }
    async addFollowRecord(args,event) {
        return await contactInfoService.addFollowRecord(args,event);
    }
    async updateFollowRecord(args,event) {
        return await contactInfoService.updateFollowRecord(args,event);
    }
    async deleteFollowRecord(args,event) {
        return await contactInfoService.deleteFollowRecord(args,event);
    }
}
ContactInfoController.toString = () => '[class ContactInfoController]';

module.exports = ContactInfoController;

/*************************************************
 ** preload为预加载模块，该文件将会在程序启动时加载 **
 *************************************************/
const { logger } = require('ee-core/log');
const { app, nativeTheme, ipcMain ,shell} = require('electron')
const { getMainWindow } = require('ee-core/electron');
const {translateService} = require("../service/translate");
const {contactInfoService} = require("../service/contactInfo");
const preload = async () => {
    ipcMainListener()
}
const ipcMainListener = ()=>{
    // 窗口控制按钮监听
    ipcMain.on('window-control', (event, data) => {
        const mainWindow = getMainWindow();
        const {action} = data;
        if (!mainWindow) return
        switch(action) {
            case 'minimize':
                mainWindow.minimize()
                break
            case 'toggle-maximize':
                mainWindow.isMaximized() ? mainWindow.unmaximize() : mainWindow.maximize()
                break
            case 'toggle-fullscreen':
                mainWindow.setFullScreen(!mainWindow.isFullScreen())
                break
            case 'close':
                mainWindow.close()
                break
        }
    })
    // 暴露一个方法用于打开链接
    ipcMain.handle('open-external-link', (event, url) => {
        shell.openExternal(url)
            .then(() => {
                logger.info(`Successfully opened the link : ${url}`);
            })
            .catch(err => {
                logger.error(`Unable to open link in external browser: ${url}`, err);
            });
    });
    //登录通知
    ipcMain.handle('online-notify', async (event, args) => {
        const { onlineStatus, avatarUrl, platform, nickName, phoneNumber, userName, msgCount } = args;
        const senderId = event.sender.id;
        const mainWin = getMainWindow();

        // 校验主窗口是否存在且未被销毁
        if (!mainWin || mainWin.isDestroyed()) {
            return; // 主窗口不存在时终止处理
        }

        const sessionObj = await app.sdb.selectOne('session_list', { windowId: senderId });
        if (sessionObj) {
            const status = String(onlineStatus);
            if (onlineStatus) {
                await app.sdb.update('session_list',
                    { onlineStatus: status, avatarUrl, userName, nickName, msgCount },
                    { platform, windowId: senderId }
                );
            } else {
                await app.sdb.update('session_list',
                    { onlineStatus: status },
                    { platform, windowId: senderId }
                );
            }
            const data = await app.sdb.selectOne('session_list', { platform, windowId: senderId });

            // 发送前再次校验窗口状态
            if (!mainWin.isDestroyed()) {
                mainWin.webContents.send('online-notify', { data });
            }
        }
    });

    //会话切换，推送配置更新
    ipcMain.handle('info-update', async (event, args) => {
        const {platform,userId} = args;
        const senderId = event.sender.id;
        const mainWin = getMainWindow();
        //推送翻译配置更新
        const trsRes = await translateService.getConfigInfo({userId:userId,platform:platform},event);
        if (trsRes.status) {
            const data = trsRes.data;
            mainWin.webContents.send('translate-config-update', {data:data});
        }
        //推送联系人信息更新
        const contactRes = await contactInfoService.getContactInfo({userId:userId,platform:platform},event);
        if (contactRes.status) {
            const data = contactRes.data;
            mainWin.webContents.send('contact-data-update', {data});
        }
    });
    //会话切换，获取翻译配置信息
    ipcMain.handle('translate-config', async (event, args) => {
        const {platform,userId} = args;
        //推送翻译配置更新
        const trsRes = await translateService.getConfigInfo({userId:userId,platform:platform},event);
        if (trsRes) {
            const data = trsRes.data;
            const route = await app.sdb.selectOne('translate_route', {name:data.translateRoute});
            if (route) {
                trsRes.data['zhName'] = route.zhName
                trsRes.data['enName'] = route.enName
            }
            return trsRes;
        }
    });
    // 文本翻译
    ipcMain.handle('text-translate', async (event, args) => {
        const { text, from, to,route ,refresh = 'false',isFilter = 'false',mode} = args;
        if (text && text.trim() && to && route) {
            //查询判断翻译服务商是否支持这个编码
            const languageObj =  await app.sdb.selectOne('language_list',{code:to})
            if (languageObj) {
                // 翻译服务
                const toCode = languageObj[route];
                if (toCode) {
                    const windowId = event.sender.id;
                    const sessionObj = await app.sdb.selectOne('session_list',{windowId:windowId});
                    const nArgs = {
                        mode:mode,
                        text:text,
                        to:toCode,
                        sourceTo:to,
                        route:route,
                        from:from,
                        partitionId:sessionObj?.partitionId,
                        isFilter:isFilter,
                    }
                    if (refresh ==='true' && isFilter === 'false') {
                       await app.sdb.delete('translate_cache',{partitionId:sessionObj.partitionId,toCode:toCode,text:text})
                    }
                    return await translateService.translateText(nArgs);
                }else {
                    return { status: false, message: `${languageObj?.zhName || route}不支持当前翻译语言！请检查翻译编码配置！`};
                }
            }else {
                return { status: false, message: `${languageObj?.zhName || route}不支持当前翻译语言！请检查翻译编码配置！`};
            }
        } else {
            logger.info('参数不能为空：',args)
            return { status: false, message: '翻译文本或编码不能为空！', data: text };
        }
    });
    // 快捷回复 调用发送函数
    ipcMain.handle('send-msg', async (event, args) => {
        const { text,partitionId,type} = args;
        if (!text || !partitionId) {
            return {status:false,message:'内容或窗口会话ID不能为空'}
        }
        const view = app.viewsMap.get(partitionId);
        if (view && !view.webContents.isDestroyed()) {
            const data = { type, text };
            await view.webContents.executeJavaScript(`quickReply(${JSON.stringify(data)})`);
        }
    });
    ipcMain.handle('theme-change', async (event, args) => {
        const { theme} = args;
        if (theme) {
            nativeTheme.themeSource = theme;
        }
    });
}

 /**
 * 预加载模块入口
 */
 module.exports = {
   preload
 }

{"name": "A2C", "version": "1.0.0", "description": "A2C SCRM", "main": "./public/electron/main.js", "scripts": {"dev": "ee-bin dev", "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt", "start": "ee-bin start", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-electron": "ee-bin build --cmds=electron", "encrypt": "ee-bin encrypt", "icon": "ee-bin icon -i /public/images/logo.png -o /build/icons/", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "ee-bin build --cmds=win64", "build-we": "ee-bin build --cmds=win_e", "build-w7z": "ee-bin build --cmds=win_7z", "build-m": "ee-bin build --cmds=mac", "build-m-arm64": "ee-bin build --cmds=mac_arm64", "build-l": "ee-bin build --cmds=linux"}, "repository": "https://github.com/dromara/electron-egg.git", "keywords": ["Electron", "electron-egg", "ElectronEgg"], "author": "<PERSON><PERSON><PERSON>", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.7.1", "@types/node": "^20.16.0", "debug": "^4.4.0", "ee-bin": "^4.1.2", "electron": "^31.0.0", "electron-builder": "^23.6.0", "icon-gen": "^5.0.0"}, "dependencies": {"@google-cloud/translate": "^8.5.1", "@vueuse/core": "^13.0.0", "axios": "^1.8.1", "better-sqlite3": "^11.5.0", "crypto-js": "^4.2.0", "ee-core": "^4.0.0", "electron-rebuild": "^3.2.9", "electron-updater": "^6.3.8", "input": "^1.0.1", "node-machine-id": "^1.1.12", "telegram": "^2.26.22", "volcengine-sdk": "^0.0.2"}}